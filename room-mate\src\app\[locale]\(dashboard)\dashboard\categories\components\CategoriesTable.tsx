"use client";

import { useState } from "react";
import { usePaginatedData } from "@/hooks/usePaginatedData";
import { getCategories, deleteCategory } from "@/actions/categories";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Edit, Trash2, Loader2 } from "lucide-react";
import { toast } from "react-hot-toast";
import { useTranslations } from "next-intl";

interface Category {
    id: string;
    name: string;
    icon: string;
    createdAt?: string;
    updatedAt?: string;
}

interface CategoriesResponse {
    data: Category[];
    pagination: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

interface CategoriesTableProps {
    onEdit: (category: Category) => void;
    refreshTrigger: number;
}

export default function CategoriesTable({ onEdit, refreshTrigger }: CategoriesTableProps) {
    const t = useTranslations("categories");
    const [deletingId, setDeletingId] = useState<string | null>(null);

    const {
        data: categories,
        isLoading,
        hasMore,
        loadMore,
        refresh,
    } = usePaginatedData<Category, CategoriesResponse>({
        queryKey: ["categories", refreshTrigger.toString()],
        fetchFn: async (page: string) => {
            const response = await getCategories({ page: parseInt(page, 10) });
            return {
                success: true,
                message: "Success",
                status: 200,
                data: response,
            };
        },
        getItems: (data: CategoriesResponse) => data.data,
        getTotalCount: (data: CategoriesResponse) => data.pagination.total,
        getItemsPerPage: (data: CategoriesResponse) => data.pagination.limit,
    });

    const handleDelete = async (id: string) => {
        if (!confirm(t("deleteConfirmation"))) return;

        setDeletingId(id);
        try {
            const response = await deleteCategory(id);
            if (response.data && !response.error) {
                toast.success(t("categoryDeleted"));
                refresh();
            } else {
                toast.error(response.error || t("deleteFailed"));
            }
        } catch (error) {
            toast.error(t("deleteFailed"));
            console.error("Delete category error:", error);
        } finally {
            setDeletingId(null);
        }
    };

    if (isLoading && categories.length === 0) {
        return (
            <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">{t("loading")}</span>
            </div>
        );
    }

    if (!isLoading && categories.length === 0) {
        return (
            <div className="text-center py-8">
                <p className="text-gray-500">{t("noCategories")}</p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="rounded-lg border shadow-sm overflow-hidden">
                <Table>
                    <TableHeader className="bg-gray-50/50">
                        <TableRow>
                            <TableHead className="text-center font-semibold">{t("icon")}</TableHead>
                            <TableHead className="text-center font-semibold">{t("name")}</TableHead>
                            <TableHead className="text-center font-semibold">{t("createdAt")}</TableHead>
                            <TableHead className="text-center font-semibold">{t("actions")}</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {categories.map((category) => (
                            <TableRow key={category.id} className="hover:bg-gray-50/50 transition-colors">
                                <TableCell className="text-center py-4">
                                    <div className="flex justify-center">
                                        <img
                                            src={category.icon}
                                            alt={category.name}
                                            className="w-12 h-12 object-cover rounded-lg shadow-sm border"
                                        />
                                    </div>
                                </TableCell>
                                <TableCell className="text-center font-medium py-4">{category.name}</TableCell>
                                <TableCell className="text-center py-4">
                                    {category.createdAt
                                        ? new Date(category.createdAt).toLocaleDateString()
                                        : '-'
                                    }
                                </TableCell>
                                <TableCell className="text-center py-4">
                                    <div className="flex justify-center space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => onEdit(category)}
                                            className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
                                        >
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleDelete(category.id)}
                                            disabled={deletingId === category.id}
                                            className="text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-300 transition-colors"
                                        >
                                            {deletingId === category.id ? (
                                                <Loader2 className="h-4 w-4 animate-spin" />
                                            ) : (
                                                <Trash2 className="h-4 w-4" />
                                            )}
                                        </Button>
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            {hasMore && (
                <div className="flex justify-center">
                    <Button
                        variant="outline"
                        onClick={loadMore}
                        disabled={isLoading}
                    >
                        {isLoading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                {t("loading")}
                            </>
                        ) : (
                            t("loadMore")
                        )}
                    </Button>
                </div>
            )}
        </div>
    );
} 
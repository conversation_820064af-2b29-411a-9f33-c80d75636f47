'use client'

import { useTranslations } from 'next-intl'
import { SlidersHorizontal, Grid3X3, List } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import { cn } from '@/lib/utils'

type SortOption = 'newest' | 'oldest' | 'price-high' | 'price-low' | 'popular'
type ViewMode = 'grid' | 'list'

interface FiltersToolbarProps {
    sortBy: SortOption
    onSortChange: (sort: SortOption) => void
    viewMode: ViewMode
    onViewModeChange: (mode: ViewMode) => void
    className?: string
}

export function FiltersToolbar({
    sortBy,
    onSortChange,
    viewMode,
    onViewModeChange,
    className
}: FiltersToolbarProps) {
    const t = useTranslations('homepage.filters')

    const sortOptions = [
        { value: 'newest', label: t('sort.newest') },
        { value: 'oldest', label: t('sort.oldest') },
        { value: 'price-high', label: t('sort.priceHigh') },
        { value: 'price-low', label: t('sort.priceLow') },
    ] as const

    return (
        <div className={cn("flex items-center gap-2", className)}>
            {/* Sort Dropdown */}
            <Select value={sortBy} onValueChange={onSortChange}>
                <SelectTrigger className="w-[140px] h-9">
                    <SlidersHorizontal className="h-4 w-4 mr-2" />
                    <SelectValue />
                </SelectTrigger>
                <SelectContent>
                    {sortOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                            {option.label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            {/* View Mode Toggle */}
            <div className="flex items-center rounded-lg bg-muted p-1">
                <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="icon"
                    className={cn(
                        "h-7 w-7 transition-all",
                        viewMode === 'grid'
                            ? "bg-background text-foreground shadow-sm"
                            : "hover:bg-transparent hover:text-foreground"
                    )}
                    onClick={() => onViewModeChange('grid')}
                    title="Grid view"
                >
                    <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="icon"
                    className={cn(
                        "h-7 w-7 transition-all",
                        viewMode === 'list'
                            ? "bg-background text-foreground shadow-sm"
                            : "hover:bg-transparent hover:text-foreground"
                    )}
                    onClick={() => onViewModeChange('list')}
                    title="List view"
                >
                    <List className="h-4 w-4" />
                </Button>
            </div>
        </div>
    )
} 
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/homepage/type-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useTranslations } from 'next-intl'\r\nimport { Building, UserCheck } from 'lucide-react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { cn } from '@/lib/utils'\r\n\r\ntype ViewType = 'properties' | 'persons'\r\n\r\ninterface TypeToggleProps {\r\n    value: ViewType\r\n    onChange: (value: ViewType) => void\r\n    className?: string\r\n}\r\n\r\nexport function TypeToggle({ value, onChange, className }: TypeToggleProps) {\r\n    const t = useTranslations('homepage.toggles')\r\n\r\n    return (\r\n        <div className={cn(\r\n            \"inline-flex items-center rounded-lg bg-muted p-1 text-muted-foreground\",\r\n            className\r\n        )}>\r\n            <Button\r\n                variant={value === 'properties' ? 'default' : 'ghost'}\r\n                size=\"icon\"\r\n                className={cn(\r\n                    \"relative h-9 w-9 transition-all focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n                    value === 'properties'\r\n                        ? \"bg-background text-foreground shadow-sm hover:text-white\"\r\n                        : \"hover:bg-transparent hover:text-foreground\"\r\n                )}\r\n                onClick={() => onChange('properties')}\r\n                title={t('properties')}\r\n            >\r\n                <Building className=\"h-5 w-5\" />\r\n            </Button>\r\n            <Button\r\n                variant={value === 'persons' ? 'default' : 'ghost'}\r\n                size=\"icon\"\r\n                className={cn(\r\n                    \"relative h-9 w-9 transition-all focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n                    value === 'persons'\r\n                        ? \"bg-background text-foreground shadow-sm hover:text-white\"\r\n                        : \"hover:bg-transparent hover:text-foreground\"\r\n                )}\r\n                onClick={() => onChange('persons')}\r\n                title={t('persons')}\r\n            >\r\n                <UserCheck className=\"h-5 w-5\" />\r\n            </Button>\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAeO,SAAS,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAmB;;IACtE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACI,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,0EACA;;0BAEA,6LAAC,qIAAA,CAAA,SAAM;gBACH,SAAS,UAAU,eAAe,YAAY;gBAC9C,MAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,4GACA,UAAU,eACJ,6DACA;gBAEV,SAAS,IAAM,SAAS;gBACxB,OAAO,EAAE;0BAET,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAExB,6LAAC,qIAAA,CAAA,SAAM;gBACH,SAAS,UAAU,YAAY,YAAY;gBAC3C,MAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,4GACA,UAAU,YACJ,6DACA;gBAEV,SAAS,IAAM,SAAS;gBACxB,OAAO,EAAE;0BAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrC;GAtCgB;;QACF,yMAAA,CAAA,kBAAe;;;KADb", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-2 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/lib/currency.ts"], "sourcesContent": ["// Currency utilities for handling location-based pricing\r\n\r\ninterface CurrencyConfig {\r\n  code: string;\r\n  symbol: string;\r\n  locale: string;\r\n  exchangeRate: number; // Rate to USD\r\n}\r\n\r\n// Currency configurations for supported countries\r\nconst CURRENCY_CONFIGS: Record<string, CurrencyConfig> = {\r\n  EG: {\r\n    code: \"EGP\",\r\n    symbol: \"ج.م\",\r\n    locale: \"ar-EG\",\r\n    exchangeRate: 50.0, // Example rate - 31 EGP = 1 USD\r\n  },\r\n  AE: {\r\n    code: \"AED\",\r\n    symbol: \"د.إ\",\r\n    locale: \"ar-AE\",\r\n    exchangeRate: 3.67, // Example rate - 3.67 AED = 1 USD\r\n  },\r\n  SA: {\r\n    code: \"SAR\",\r\n    symbol: \"ر.س\",\r\n    locale: \"ar-SA\",\r\n    exchangeRate: 3.75, // Example rate - 3.75 SAR = 1 USD\r\n  },\r\n  JO: {\r\n    code: \"JOD\",\r\n    symbol: \"د.أ\",\r\n    locale: \"ar-JO\",\r\n    exchangeRate: 0.71, // Example rate - 0.71 JOD = 1 USD\r\n  },\r\n  US: {\r\n    code: \"USD\",\r\n    symbol: \"$\",\r\n    locale: \"en-US\",\r\n    exchangeRate: 1.0,\r\n  },\r\n  DEFAULT: {\r\n    code: \"USD\",\r\n    symbol: \"$\",\r\n    locale: \"en-US\",\r\n    exchangeRate: 1.0,\r\n  },\r\n};\r\n\r\n// Country code mapping based on browser location\r\nconst COUNTRY_CODE_MAPPING: Record<string, string> = {\r\n  Egypt: \"EG\",\r\n  \"United Arab Emirates\": \"AE\",\r\n  UAE: \"AE\",\r\n  \"Saudi Arabia\": \"SA\",\r\n  Jordan: \"JO\",\r\n  \"United States\": \"US\",\r\n  USA: \"US\",\r\n};\r\n\r\ninterface UserLocation {\r\n  country: string;\r\n  countryCode: string;\r\n  currency: CurrencyConfig;\r\n}\r\n\r\ninterface CurrencyFormatOptions {\r\n  showSymbol?: boolean;\r\n  showCode?: boolean;\r\n  locale?: \"en\" | \"ar\";\r\n}\r\n\r\nclass CurrencyManager {\r\n  private userLocation: UserLocation | null = null;\r\n  private exchangeRates: Record<string, number> = {};\r\n  private lastRateUpdate: Date | null = null;\r\n\r\n  // Cache for 1 hour\r\n  private readonly CACHE_DURATION = 60 * 60 * 1000;\r\n\r\n  /**\r\n   * Initialize currency manager and detect user location\r\n   */\r\n  async init(): Promise<void> {\r\n    try {\r\n      // Try to get cached location first\r\n      const cachedLocation = this.getCachedLocation();\r\n      if (cachedLocation) {\r\n        this.userLocation = cachedLocation;\r\n        return;\r\n      }\r\n\r\n      // Detect user location\r\n      const location = await this.detectUserLocation();\r\n      this.userLocation = location;\r\n\r\n      // Cache the location\r\n      this.setCachedLocation(location);\r\n\r\n      // Update exchange rates\r\n      await this.updateExchangeRates();\r\n    } catch (error) {\r\n      console.error(\"Failed to initialize currency manager:\", error);\r\n      // Fallback to default currency\r\n      this.userLocation = {\r\n        country: \"Default\",\r\n        countryCode: \"DEFAULT\",\r\n        currency: CURRENCY_CONFIGS.DEFAULT,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Detect user location using multiple methods\r\n   */\r\n  private async detectUserLocation(): Promise<UserLocation> {\r\n    // Method 1: Try geolocation API with reverse geocoding\r\n    try {\r\n      const geoLocation = await this.getGeolocation();\r\n      const country = await this.reverseGeocode(\r\n        geoLocation.latitude,\r\n        geoLocation.longitude\r\n      );\r\n      return this.createLocationFromCountry(country);\r\n    } catch (error) {\r\n      console.log(\"Geolocation failed, trying IP-based detection\");\r\n    }\r\n\r\n    // Method 2: Try IP-based location detection\r\n    try {\r\n      const ipLocation = await this.getLocationFromIP();\r\n      return this.createLocationFromCountry(ipLocation.country);\r\n    } catch (error) {\r\n      console.log(\"IP-based location failed, trying browser locale\");\r\n    }\r\n\r\n    // Method 3: Fallback to browser locale\r\n    try {\r\n      const browserCountry = this.getCountryFromBrowserLocale();\r\n      return this.createLocationFromCountry(browserCountry);\r\n    } catch (error) {\r\n      console.log(\"Browser locale failed, using default\");\r\n    }\r\n\r\n    // Method 4: Default fallback\r\n    return {\r\n      country: \"Default\",\r\n      countryCode: \"DEFAULT\",\r\n      currency: CURRENCY_CONFIGS.DEFAULT,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get user location using browser geolocation API\r\n   */\r\n  private getGeolocation(): Promise<{ latitude: number; longitude: number }> {\r\n    return new Promise((resolve, reject) => {\r\n      if (!navigator.geolocation) {\r\n        reject(new Error(\"Geolocation is not supported\"));\r\n        return;\r\n      }\r\n\r\n      navigator.geolocation.getCurrentPosition(\r\n        (position) => {\r\n          resolve({\r\n            latitude: position.coords.latitude,\r\n            longitude: position.coords.longitude,\r\n          });\r\n        },\r\n        (error) => {\r\n          reject(error);\r\n        },\r\n        {\r\n          timeout: 10000,\r\n          maximumAge: 600000, // 10 minutes\r\n          enableHighAccuracy: false,\r\n        }\r\n      );\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Reverse geocode coordinates to get country\r\n   */\r\n  private async reverseGeocode(lat: number, lng: number): Promise<string> {\r\n    const response = await fetch(\r\n      `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Reverse geocoding failed\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    return data.countryName || \"Default\";\r\n  }\r\n\r\n  /**\r\n   * Get location from IP address\r\n   */\r\n  private async getLocationFromIP(): Promise<{ country: string }> {\r\n    const response = await fetch(\"https://ipapi.co/json/\");\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"IP-based location detection failed\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    return { country: data.country_name || \"Default\" };\r\n  }\r\n\r\n  /**\r\n   * Get country from browser locale\r\n   */\r\n  private getCountryFromBrowserLocale(): string {\r\n    const locale = navigator.language || navigator.languages?.[0] || \"en-US\";\r\n\r\n    // Extract country code from locale (e.g., 'ar-SA' -> 'SA')\r\n    const countryCode = locale.split(\"-\")[1];\r\n\r\n    if (countryCode) {\r\n      // Map country codes to full names\r\n      const countryMap: Record<string, string> = {\r\n        EG: \"Egypt\",\r\n        AE: \"United Arab Emirates\",\r\n        SA: \"Saudi Arabia\",\r\n        JO: \"Jordan\",\r\n        US: \"United States\",\r\n      };\r\n\r\n      return countryMap[countryCode] || \"Default\";\r\n    }\r\n\r\n    return \"Default\";\r\n  }\r\n\r\n  /**\r\n   * Create location object from country name\r\n   */\r\n  private createLocationFromCountry(country: string): UserLocation {\r\n    const countryCode = COUNTRY_CODE_MAPPING[country] || \"DEFAULT\";\r\n    const currency = CURRENCY_CONFIGS[countryCode] || CURRENCY_CONFIGS.DEFAULT;\r\n\r\n    return {\r\n      country,\r\n      countryCode,\r\n      currency,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Update exchange rates from external API\r\n   */\r\n  private async updateExchangeRates(): Promise<void> {\r\n    try {\r\n      // Check if rates are still fresh\r\n      if (\r\n        this.lastRateUpdate &&\r\n        Date.now() - this.lastRateUpdate.getTime() < this.CACHE_DURATION\r\n      ) {\r\n        return;\r\n      }\r\n\r\n      // Use a free exchange rate API\r\n      const response = await fetch(\r\n        \"https://api.exchangerate-api.com/v4/latest/USD\"\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch exchange rates\");\r\n      }\r\n\r\n      const data = await response.json();\r\n      this.exchangeRates = data.rates;\r\n      this.lastRateUpdate = new Date();\r\n\r\n      // Update currency configs with live rates\r\n      Object.keys(CURRENCY_CONFIGS).forEach((countryCode) => {\r\n        const config = CURRENCY_CONFIGS[countryCode];\r\n        if (this.exchangeRates[config.code]) {\r\n          config.exchangeRate = this.exchangeRates[config.code];\r\n        }\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to update exchange rates:\", error);\r\n      // Continue with cached/default rates\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convert price from USD to user's local currency\r\n   */\r\n  convertPrice(usdPrice: number): number {\r\n    if (!this.userLocation) {\r\n      return usdPrice;\r\n    }\r\n\r\n    return usdPrice * this.userLocation.currency.exchangeRate;\r\n  }\r\n\r\n  /**\r\n   * Convert price from property's local currency to user's location currency\r\n   */\r\n  formatPropertyPrice(\r\n    localPrice: number | string,\r\n    propertyCountryCode: string,\r\n    options: CurrencyFormatOptions = {}\r\n  ): string {\r\n    const { showSymbol = true, showCode = false, locale = \"en\" } = options;\r\n\r\n    const numPrice =\r\n      typeof localPrice === \"string\" ? parseFloat(localPrice) : localPrice;\r\n    if (isNaN(numPrice)) return localPrice?.toString() || \"0\";\r\n\r\n    // Initialize exchange rates if not already done (async, non-blocking)\r\n    if (!this.lastRateUpdate) {\r\n      this.updateExchangeRates().catch(console.error);\r\n    }\r\n\r\n    // Get property's currency config\r\n    const propertyCurrency =\r\n      CURRENCY_CONFIGS[propertyCountryCode] || CURRENCY_CONFIGS.DEFAULT;\r\n\r\n    // Determine target currency based on user location\r\n    let targetCurrency: CurrencyConfig;\r\n    let formatLocale: string;\r\n\r\n    if (\r\n      this.userLocation &&\r\n      [\"EG\", \"AE\", \"SA\", \"JO\"].includes(this.userLocation.countryCode)\r\n    ) {\r\n      // User is in one of our supported countries\r\n      targetCurrency = this.userLocation.currency;\r\n      formatLocale = locale === \"ar\" ? targetCurrency.locale : \"en-US\";\r\n    } else {\r\n      // User location not in supported countries, use USD\r\n      targetCurrency = CURRENCY_CONFIGS.DEFAULT;\r\n      formatLocale = \"en-US\";\r\n    }\r\n\r\n    // Convert: Local Currency → USD → Target Currency\r\n    let convertedPrice: number;\r\n\r\n    if (propertyCurrency.code === targetCurrency.code) {\r\n      // Same currency, no conversion needed\r\n      convertedPrice = numPrice;\r\n    } else {\r\n      // Convert property local currency to USD first\r\n      const usdPrice = numPrice / propertyCurrency.exchangeRate;\r\n      // Then convert USD to target currency\r\n      convertedPrice = usdPrice * targetCurrency.exchangeRate;\r\n    }\r\n\r\n    if (showSymbol || showCode) {\r\n      // Format with currency\r\n      try {\r\n        const formatted = new Intl.NumberFormat(formatLocale, {\r\n          style: \"currency\",\r\n          currency: targetCurrency.code,\r\n          currencyDisplay: showCode ? \"code\" : \"symbol\",\r\n        }).format(convertedPrice);\r\n\r\n        return formatted;\r\n      } catch (error) {\r\n        // Fallback if currency is not supported\r\n        const numberFormatted = new Intl.NumberFormat(formatLocale).format(\r\n          convertedPrice\r\n        );\r\n        return showSymbol\r\n          ? `${targetCurrency.symbol}${numberFormatted}`\r\n          : numberFormatted;\r\n      }\r\n    } else {\r\n      // Format number only\r\n      return new Intl.NumberFormat(formatLocale).format(convertedPrice);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Format price according to user's locale and currency (for backward compatibility)\r\n   */\r\n  formatPrice(\r\n    usdPrice: number | string,\r\n    options: CurrencyFormatOptions = {}\r\n  ): string {\r\n    const { showSymbol = true, showCode = false, locale = \"en\" } = options;\r\n\r\n    if (!this.userLocation) {\r\n      // Fallback formatting\r\n      const numPrice =\r\n        typeof usdPrice === \"string\" ? parseFloat(usdPrice) : usdPrice;\r\n      if (isNaN(numPrice)) return usdPrice.toString();\r\n\r\n      return new Intl.NumberFormat(\"en-US\", {\r\n        style: \"currency\",\r\n        currency: \"USD\",\r\n      }).format(numPrice);\r\n    }\r\n\r\n    const numPrice =\r\n      typeof usdPrice === \"string\" ? parseFloat(usdPrice) : usdPrice;\r\n    if (isNaN(numPrice)) return usdPrice.toString();\r\n\r\n    // Convert to local currency\r\n    const localPrice = this.convertPrice(numPrice);\r\n    const currency = this.userLocation.currency;\r\n\r\n    // Use appropriate locale for formatting\r\n    const formatLocale = locale === \"ar\" ? currency.locale : \"en-US\";\r\n\r\n    if (showSymbol || showCode) {\r\n      // Format with currency\r\n      try {\r\n        const formatted = new Intl.NumberFormat(formatLocale, {\r\n          style: \"currency\",\r\n          currency: currency.code,\r\n          currencyDisplay: showCode ? \"code\" : \"symbol\",\r\n        }).format(localPrice);\r\n\r\n        return formatted;\r\n      } catch (error) {\r\n        // Fallback if currency is not supported\r\n        const numberFormatted = new Intl.NumberFormat(formatLocale).format(\r\n          localPrice\r\n        );\r\n        return showSymbol\r\n          ? `${currency.symbol}${numberFormatted}`\r\n          : numberFormatted;\r\n      }\r\n    } else {\r\n      // Format number only\r\n      return new Intl.NumberFormat(formatLocale).format(localPrice);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get user's currency information\r\n   */\r\n  getUserCurrency(): CurrencyConfig | null {\r\n    return this.userLocation?.currency || null;\r\n  }\r\n\r\n  /**\r\n   * Get user's location information\r\n   */\r\n  getUserLocation(): UserLocation | null {\r\n    return this.userLocation;\r\n  }\r\n\r\n  /**\r\n   * Cache location in localStorage\r\n   */\r\n  private setCachedLocation(location: UserLocation): void {\r\n    try {\r\n      const cacheData = {\r\n        location,\r\n        timestamp: Date.now(),\r\n      };\r\n      localStorage.setItem(\"userLocation\", JSON.stringify(cacheData));\r\n    } catch (error) {\r\n      console.error(\"Failed to cache location:\", error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cached location from localStorage\r\n   */\r\n  private getCachedLocation(): UserLocation | null {\r\n    try {\r\n      const cached = localStorage.getItem(\"userLocation\");\r\n      if (!cached) return null;\r\n\r\n      const cacheData = JSON.parse(cached);\r\n\r\n      // Check if cache is still valid (24 hours)\r\n      if (Date.now() - cacheData.timestamp > 24 * 60 * 60 * 1000) {\r\n        localStorage.removeItem(\"userLocation\");\r\n        return null;\r\n      }\r\n\r\n      return cacheData.location;\r\n    } catch (error) {\r\n      console.error(\"Failed to get cached location:\", error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Force refresh location and rates\r\n   */\r\n  async refresh(): Promise<void> {\r\n    localStorage.removeItem(\"userLocation\");\r\n    this.userLocation = null;\r\n    this.lastRateUpdate = null;\r\n    await this.init();\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const currencyManager = new CurrencyManager();\r\n\r\n/**\r\n * Map property country name to currency country code\r\n */\r\nexport function getCountryCodeFromName(countryName: string): string {\r\n  // Handle common variations and mappings\r\n  const normalizedCountry = countryName?.toUpperCase().trim();\r\n\r\n  const countryMappings: Record<string, string> = {\r\n    EGYPT: \"EG\",\r\n    EG: \"EG\",\r\n    \"UNITED ARAB EMIRATES\": \"AE\",\r\n    UAE: \"AE\",\r\n    AE: \"AE\",\r\n    \"SAUDI ARABIA\": \"SA\",\r\n    SA: \"SA\",\r\n    JORDAN: \"JO\",\r\n    JO: \"JO\",\r\n    \"UNITED STATES\": \"US\",\r\n    USA: \"US\",\r\n    US: \"US\",\r\n  };\r\n\r\n  return countryMappings[normalizedCountry] || \"DEFAULT\";\r\n}\r\n\r\n// Export hook for React components\r\nexport function useCurrency() {\r\n  return {\r\n    formatPrice: (price: number | string, options?: CurrencyFormatOptions) =>\r\n      currencyManager.formatPrice(price, options),\r\n    formatPropertyPrice: (\r\n      price: number | string,\r\n      countryCode: string,\r\n      options?: CurrencyFormatOptions\r\n    ) => currencyManager.formatPropertyPrice(price, countryCode, options),\r\n    getUserCurrency: () => currencyManager.getUserCurrency(),\r\n    getUserLocation: () => currencyManager.getUserLocation(),\r\n    refresh: () => currencyManager.refresh(),\r\n  };\r\n}\r\n\r\n// Auto-initialize when module loads\r\nif (typeof window !== \"undefined\") {\r\n  currencyManager.init().catch(console.error);\r\n}\r\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;AASzD,kDAAkD;AAClD,MAAM,mBAAmD;IACvD,IAAI;QACF,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;IACA,IAAI;QACF,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;IACA,IAAI;QACF,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;IACA,IAAI;QACF,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;IACA,IAAI;QACF,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;IACA,SAAS;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;AACF;AAEA,iDAAiD;AACjD,MAAM,uBAA+C;IACnD,OAAO;IACP,wBAAwB;IACxB,KAAK;IACL,gBAAgB;IAChB,QAAQ;IACR,iBAAiB;IACjB,KAAK;AACP;AAcA,MAAM;IACI,eAAoC,KAAK;IACzC,gBAAwC,CAAC,EAAE;IAC3C,iBAA8B,KAAK;IAE3C,mBAAmB;IACF,iBAAiB,KAAK,KAAK,KAAK;IAEjD;;GAEC,GACD,MAAM,OAAsB;QAC1B,IAAI;YACF,mCAAmC;YACnC,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;YAC7C,IAAI,gBAAgB;gBAClB,IAAI,CAAC,YAAY,GAAG;gBACpB;YACF;YAEA,uBAAuB;YACvB,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB;YAC9C,IAAI,CAAC,YAAY,GAAG;YAEpB,qBAAqB;YACrB,IAAI,CAAC,iBAAiB,CAAC;YAEvB,wBAAwB;YACxB,MAAM,IAAI,CAAC,mBAAmB;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,+BAA+B;YAC/B,IAAI,CAAC,YAAY,GAAG;gBAClB,SAAS;gBACT,aAAa;gBACb,UAAU,iBAAiB,OAAO;YACpC;QACF;IACF;IAEA;;GAEC,GACD,MAAc,qBAA4C;QACxD,uDAAuD;QACvD,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc;YAC7C,MAAM,UAAU,MAAM,IAAI,CAAC,cAAc,CACvC,YAAY,QAAQ,EACpB,YAAY,SAAS;YAEvB,OAAO,IAAI,CAAC,yBAAyB,CAAC;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,4CAA4C;QAC5C,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,iBAAiB;YAC/C,OAAO,IAAI,CAAC,yBAAyB,CAAC,WAAW,OAAO;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,uCAAuC;QACvC,IAAI;YACF,MAAM,iBAAiB,IAAI,CAAC,2BAA2B;YACvD,OAAO,IAAI,CAAC,yBAAyB,CAAC;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,6BAA6B;QAC7B,OAAO;YACL,SAAS;YACT,aAAa;YACb,UAAU,iBAAiB,OAAO;QACpC;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAmE;QACzE,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,UAAU,WAAW,EAAE;gBAC1B,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;gBACC,QAAQ;oBACN,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;gBACtC;YACF,GACA,CAAC;gBACC,OAAO;YACT,GACA;gBACE,SAAS;gBACT,YAAY;gBACZ,oBAAoB;YACtB;QAEJ;IACF;IAEA;;GAEC,GACD,MAAc,eAAe,GAAW,EAAE,GAAW,EAAmB;QACtE,MAAM,WAAW,MAAM,MACrB,CAAC,kEAAkE,EAAE,IAAI,WAAW,EAAE,IAAI,oBAAoB,CAAC;QAGjH,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,WAAW,IAAI;IAC7B;IAEA;;GAEC,GACD,MAAc,oBAAkD;QAC9D,MAAM,WAAW,MAAM,MAAM;QAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;YAAE,SAAS,KAAK,YAAY,IAAI;QAAU;IACnD;IAEA;;GAEC,GACD,AAAQ,8BAAsC;QAC5C,MAAM,SAAS,UAAU,QAAQ,IAAI,UAAU,SAAS,EAAE,CAAC,EAAE,IAAI;QAEjE,2DAA2D;QAC3D,MAAM,cAAc,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;QAExC,IAAI,aAAa;YACf,kCAAkC;YAClC,MAAM,aAAqC;gBACzC,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;YACN;YAEA,OAAO,UAAU,CAAC,YAAY,IAAI;QACpC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,0BAA0B,OAAe,EAAgB;QAC/D,MAAM,cAAc,oBAAoB,CAAC,QAAQ,IAAI;QACrD,MAAM,WAAW,gBAAgB,CAAC,YAAY,IAAI,iBAAiB,OAAO;QAE1E,OAAO;YACL;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,MAAc,sBAAqC;QACjD,IAAI;YACF,iCAAiC;YACjC,IACE,IAAI,CAAC,cAAc,IACnB,KAAK,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,IAAI,CAAC,cAAc,EAChE;gBACA;YACF;YAEA,+BAA+B;YAC/B,MAAM,WAAW,MAAM,MACrB;YAGF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK;YAC/B,IAAI,CAAC,cAAc,GAAG,IAAI;YAE1B,0CAA0C;YAC1C,OAAO,IAAI,CAAC,kBAAkB,OAAO,CAAC,CAAC;gBACrC,MAAM,SAAS,gBAAgB,CAAC,YAAY;gBAC5C,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,EAAE;oBACnC,OAAO,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC;gBACvD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,qCAAqC;QACvC;IACF;IAEA;;GAEC,GACD,aAAa,QAAgB,EAAU;QACrC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,OAAO;QACT;QAEA,OAAO,WAAW,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY;IAC3D;IAEA;;GAEC,GACD,oBACE,UAA2B,EAC3B,mBAA2B,EAC3B,UAAiC,CAAC,CAAC,EAC3B;QACR,MAAM,EAAE,aAAa,IAAI,EAAE,WAAW,KAAK,EAAE,SAAS,IAAI,EAAE,GAAG;QAE/D,MAAM,WACJ,OAAO,eAAe,WAAW,WAAW,cAAc;QAC5D,IAAI,MAAM,WAAW,OAAO,YAAY,cAAc;QAEtD,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,QAAQ,KAAK;QAChD;QAEA,iCAAiC;QACjC,MAAM,mBACJ,gBAAgB,CAAC,oBAAoB,IAAI,iBAAiB,OAAO;QAEnE,mDAAmD;QACnD,IAAI;QACJ,IAAI;QAEJ,IACE,IAAI,CAAC,YAAY,IACjB;YAAC;YAAM;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,GAC/D;YACA,4CAA4C;YAC5C,iBAAiB,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC3C,eAAe,WAAW,OAAO,eAAe,MAAM,GAAG;QAC3D,OAAO;YACL,oDAAoD;YACpD,iBAAiB,iBAAiB,OAAO;YACzC,eAAe;QACjB;QAEA,kDAAkD;QAClD,IAAI;QAEJ,IAAI,iBAAiB,IAAI,KAAK,eAAe,IAAI,EAAE;YACjD,sCAAsC;YACtC,iBAAiB;QACnB,OAAO;YACL,+CAA+C;YAC/C,MAAM,WAAW,WAAW,iBAAiB,YAAY;YACzD,sCAAsC;YACtC,iBAAiB,WAAW,eAAe,YAAY;QACzD;QAEA,IAAI,cAAc,UAAU;YAC1B,uBAAuB;YACvB,IAAI;gBACF,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,cAAc;oBACpD,OAAO;oBACP,UAAU,eAAe,IAAI;oBAC7B,iBAAiB,WAAW,SAAS;gBACvC,GAAG,MAAM,CAAC;gBAEV,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,wCAAwC;gBACxC,MAAM,kBAAkB,IAAI,KAAK,YAAY,CAAC,cAAc,MAAM,CAChE;gBAEF,OAAO,aACH,GAAG,eAAe,MAAM,GAAG,iBAAiB,GAC5C;YACN;QACF,OAAO;YACL,qBAAqB;YACrB,OAAO,IAAI,KAAK,YAAY,CAAC,cAAc,MAAM,CAAC;QACpD;IACF;IAEA;;GAEC,GACD,YACE,QAAyB,EACzB,UAAiC,CAAC,CAAC,EAC3B;QACR,MAAM,EAAE,aAAa,IAAI,EAAE,WAAW,KAAK,EAAE,SAAS,IAAI,EAAE,GAAG;QAE/D,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,sBAAsB;YACtB,MAAM,WACJ,OAAO,aAAa,WAAW,WAAW,YAAY;YACxD,IAAI,MAAM,WAAW,OAAO,SAAS,QAAQ;YAE7C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;gBACpC,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QACZ;QAEA,MAAM,WACJ,OAAO,aAAa,WAAW,WAAW,YAAY;QACxD,IAAI,MAAM,WAAW,OAAO,SAAS,QAAQ;QAE7C,4BAA4B;QAC5B,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC;QACrC,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,QAAQ;QAE3C,wCAAwC;QACxC,MAAM,eAAe,WAAW,OAAO,SAAS,MAAM,GAAG;QAEzD,IAAI,cAAc,UAAU;YAC1B,uBAAuB;YACvB,IAAI;gBACF,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,cAAc;oBACpD,OAAO;oBACP,UAAU,SAAS,IAAI;oBACvB,iBAAiB,WAAW,SAAS;gBACvC,GAAG,MAAM,CAAC;gBAEV,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,wCAAwC;gBACxC,MAAM,kBAAkB,IAAI,KAAK,YAAY,CAAC,cAAc,MAAM,CAChE;gBAEF,OAAO,aACH,GAAG,SAAS,MAAM,GAAG,iBAAiB,GACtC;YACN;QACF,OAAO;YACL,qBAAqB;YACrB,OAAO,IAAI,KAAK,YAAY,CAAC,cAAc,MAAM,CAAC;QACpD;IACF;IAEA;;GAEC,GACD,kBAAyC;QACvC,OAAO,IAAI,CAAC,YAAY,EAAE,YAAY;IACxC;IAEA;;GAEC,GACD,kBAAuC;QACrC,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA;;GAEC,GACD,AAAQ,kBAAkB,QAAsB,EAAQ;QACtD,IAAI;YACF,MAAM,YAAY;gBAChB;gBACA,WAAW,KAAK,GAAG;YACrB;YACA,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA;;GAEC,GACD,AAAQ,oBAAyC;QAC/C,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,CAAC,QAAQ,OAAO;YAEpB,MAAM,YAAY,KAAK,KAAK,CAAC;YAE7B,2CAA2C;YAC3C,IAAI,KAAK,GAAG,KAAK,UAAU,SAAS,GAAG,KAAK,KAAK,KAAK,MAAM;gBAC1D,aAAa,UAAU,CAAC;gBACxB,OAAO;YACT;YAEA,OAAO,UAAU,QAAQ;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,UAAyB;QAC7B,aAAa,UAAU,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,cAAc,GAAG;QACtB,MAAM,IAAI,CAAC,IAAI;IACjB;AACF;AAGO,MAAM,kBAAkB,IAAI;AAK5B,SAAS,uBAAuB,WAAmB;IACxD,wCAAwC;IACxC,MAAM,oBAAoB,aAAa,cAAc;IAErD,MAAM,kBAA0C;QAC9C,OAAO;QACP,IAAI;QACJ,wBAAwB;QACxB,KAAK;QACL,IAAI;QACJ,gBAAgB;QAChB,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,iBAAiB;QACjB,KAAK;QACL,IAAI;IACN;IAEA,OAAO,eAAe,CAAC,kBAAkB,IAAI;AAC/C;AAGO,SAAS;IACd,OAAO;QACL,aAAa,CAAC,OAAwB,UACpC,gBAAgB,WAAW,CAAC,OAAO;QACrC,qBAAqB,CACnB,OACA,aACA,UACG,gBAAgB,mBAAmB,CAAC,OAAO,aAAa;QAC7D,iBAAiB,IAAM,gBAAgB,eAAe;QACtD,iBAAiB,IAAM,gBAAgB,eAAe;QACtD,SAAS,IAAM,gBAAgB,OAAO;IACxC;AACF;AAEA,oCAAoC;AACpC,wCAAmC;IACjC,gBAAgB,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK;AAC5C", "debugId": null}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/hooks/useCurrencyState.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from \"react\";\r\nimport { currencyManager, getCountryCodeFromName } from \"@/lib/currency\";\r\n\r\ninterface CurrencyConfig {\r\n  code: string;\r\n  symbol: string;\r\n  locale: string;\r\n  exchangeRate: number;\r\n}\r\n\r\ninterface UserLocation {\r\n  country: string;\r\n  countryCode: string;\r\n  currency: CurrencyConfig;\r\n}\r\n\r\ninterface CurrencyFormatOptions {\r\n  showSymbol?: boolean;\r\n  showCode?: boolean;\r\n  locale?: \"en\" | \"ar\";\r\n}\r\n\r\ninterface CurrencyState {\r\n  isLoading: boolean;\r\n  userLocation: UserLocation | null;\r\n  userCurrency: CurrencyConfig | null;\r\n  error: string | null;\r\n}\r\n\r\n/**\r\n * React hook for managing currency state and formatting\r\n */\r\nexport function useCurrencyState() {\r\n  const [state, setState] = useState<CurrencyState>({\r\n    isLoading: true,\r\n    userLocation: null,\r\n    userCurrency: null,\r\n    error: null,\r\n  });\r\n\r\n  // Initialize currency manager and update state\r\n  const initializeCurrency = useCallback(async () => {\r\n    try {\r\n      setState((prev) => ({ ...prev, isLoading: true, error: null }));\r\n\r\n      // Initialize currency manager\r\n      await currencyManager.init();\r\n\r\n      // Get location and currency info\r\n      const location = currencyManager.getUserLocation();\r\n      const currency = currencyManager.getUserCurrency();\r\n\r\n      setState({\r\n        isLoading: false,\r\n        userLocation: location,\r\n        userCurrency: currency,\r\n        error: null,\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to initialize currency:\", error);\r\n      setState({\r\n        isLoading: false,\r\n        userLocation: null,\r\n        userCurrency: null,\r\n        error:\r\n          error instanceof Error\r\n            ? error.message\r\n            : \"Failed to initialize currency\",\r\n      });\r\n    }\r\n  }, []);\r\n\r\n  // Format price using the currency manager (user location-based)\r\n  const formatPrice = useCallback(\r\n    (price: number | string, options: CurrencyFormatOptions = {}): string => {\r\n      return currencyManager.formatPrice(price, options);\r\n    },\r\n    []\r\n  );\r\n\r\n  // Format price based on property country (property currency -> user currency)\r\n  const formatPropertyPrice = useCallback(\r\n    (\r\n      price: number | string,\r\n      countryName: string,\r\n      options: CurrencyFormatOptions = {}\r\n    ): string => {\r\n      const countryCode = getCountryCodeFromName(countryName);\r\n      return currencyManager.formatPropertyPrice(price, countryCode, options);\r\n    },\r\n    []\r\n  );\r\n\r\n  // Refresh currency data\r\n  const refresh = useCallback(async () => {\r\n    await currencyManager.refresh();\r\n    await initializeCurrency();\r\n  }, [initializeCurrency]);\r\n\r\n  // Initialize on mount\r\n  useEffect(() => {\r\n    initializeCurrency();\r\n  }, [initializeCurrency]);\r\n\r\n  return {\r\n    // State\r\n    isLoading: state.isLoading,\r\n    userLocation: state.userLocation,\r\n    userCurrency: state.userCurrency,\r\n    error: state.error,\r\n\r\n    // Methods\r\n    formatPrice,\r\n    formatPropertyPrice,\r\n    refresh,\r\n\r\n    // Utility getters\r\n    isReady: !state.isLoading && !state.error && state.userCurrency !== null,\r\n    currencyCode: state.userCurrency?.code || \"USD\",\r\n    currencySymbol: state.userCurrency?.symbol || \"$\",\r\n    countryCode: state.userLocation?.countryCode || \"DEFAULT\",\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAkCO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,WAAW;QACX,cAAc;QACd,cAAc;QACd,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,IAAI;gBACF;wEAAS,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE,WAAW;4BAAM,OAAO;wBAAK,CAAC;;gBAE7D,8BAA8B;gBAC9B,MAAM,yHAAA,CAAA,kBAAe,CAAC,IAAI;gBAE1B,iCAAiC;gBACjC,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,eAAe;gBAChD,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,eAAe;gBAEhD,SAAS;oBACP,WAAW;oBACX,cAAc;oBACd,cAAc;oBACd,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SAAS;oBACP,WAAW;oBACX,cAAc;oBACd,cAAc;oBACd,OACE,iBAAiB,QACb,MAAM,OAAO,GACb;gBACR;YACF;QACF;2DAAG,EAAE;IAEL,gEAAgE;IAChE,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC5B,CAAC,OAAwB,UAAiC,CAAC,CAAC;YAC1D,OAAO,yHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,OAAO;QAC5C;oDACA,EAAE;IAGJ,8EAA8E;IAC9E,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DACpC,CACE,OACA,aACA,UAAiC,CAAC,CAAC;YAEnC,MAAM,cAAc,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAE;YAC3C,OAAO,yHAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,OAAO,aAAa;QACjE;4DACA,EAAE;IAGJ,wBAAwB;IACxB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC1B,MAAM,yHAAA,CAAA,kBAAe,CAAC,OAAO;YAC7B,MAAM;QACR;gDAAG;QAAC;KAAmB;IAEvB,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAmB;IAEvB,OAAO;QACL,QAAQ;QACR,WAAW,MAAM,SAAS;QAC1B,cAAc,MAAM,YAAY;QAChC,cAAc,MAAM,YAAY;QAChC,OAAO,MAAM,KAAK;QAElB,UAAU;QACV;QACA;QACA;QAEA,kBAAkB;QAClB,SAAS,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,YAAY,KAAK;QACpE,cAAc,MAAM,YAAY,EAAE,QAAQ;QAC1C,gBAAgB,MAAM,YAAY,EAAE,UAAU;QAC9C,aAAa,MAAM,YAAY,EAAE,eAAe;IAClD;AACF;GA1FgB", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/homepage/property-card.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\nimport { Heart, Bath, Users, Square, Bed, ChevronLeft, ChevronRight } from 'lucide-react'\r\nimport { Card, CardContent } from '@/components/ui/card'\r\nimport { Button } from '@/components/ui/button'\r\nimport { useCurrencyState } from '@/hooks/useCurrencyState'\r\nimport { cn } from '@/lib/utils'\r\nimport { useRouter } from '@/i18n/navigation'\r\n\r\n\r\ninterface Property {\r\n    id: string\r\n    title: string\r\n    slug: string\r\n    images: string[]\r\n    city: string\r\n    country: string\r\n    neighborhood: string\r\n    address: string\r\n    description: string\r\n    type: string\r\n    roomType: string\r\n    genderRequired: string\r\n    totalRooms: string\r\n    availableRooms: string\r\n    price: string\r\n    size: string\r\n    floor: string\r\n    bathrooms: string\r\n    separatedBathroom: boolean\r\n    residentsCount: string\r\n    availablePersons: string\r\n    rentTime: string\r\n    paymentTime: string\r\n    priceIncludeWaterAndElectricity: boolean\r\n    allowSmoking: boolean\r\n    includeFurniture: boolean\r\n    airConditioning: boolean\r\n    includeWaterHeater: boolean\r\n    parking: boolean\r\n    internet: boolean\r\n    nearToMetro: boolean\r\n    nearToMarket: boolean\r\n    elevator: boolean\r\n    trialPeriod: boolean\r\n    goodForForeigners: boolean\r\n    categoryId: string\r\n    isVerified?: boolean\r\n    isAvailable?: boolean\r\n    isFavorite?: boolean\r\n    rating?: number\r\n    totalRatings?: number\r\n    user?: {\r\n        name: string\r\n        avatar?: string\r\n    }\r\n    createdAt: string\r\n    updatedAt: string\r\n}\r\n\r\ninterface PropertyCardProps {\r\n    property: Property\r\n    onFavoriteToggle?: (propertyId: string) => void\r\n    className?: string,\r\n    viewType?: 'properties' | 'persons'\r\n    isFavoriteLoading?: boolean\r\n    isFavorite?: boolean\r\n}\r\n\r\n// Custom Gender Icons\r\nconst MaleIcon = ({ className }: { className?: string }) => (\r\n    <svg\r\n        className={className}\r\n        viewBox=\"0 0 24 24\"\r\n        fill=\"none\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"2\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n    >\r\n        <circle cx=\"10\" cy=\"14\" r=\"8\" />\r\n        <path d=\"M16.93 6.07l4-4\" />\r\n        <path d=\"M21 2h-5v5\" />\r\n    </svg>\r\n)\r\n\r\nconst FemaleIcon = ({ className }: { className?: string }) => (\r\n    <svg\r\n        className={className}\r\n        viewBox=\"0 0 24 24\"\r\n        fill=\"none\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"2\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n    >\r\n        <circle cx=\"12\" cy=\"8\" r=\"7\" />\r\n        <path d=\"M12 15v7\" />\r\n        <path d=\"M8 19h8\" />\r\n    </svg>\r\n)\r\n\r\nexport function PropertyCard({ property, onFavoriteToggle, className, viewType, isFavoriteLoading = false, isFavorite = false }: PropertyCardProps) {\r\n    const router = useRouter()\r\n    const locale = useLocale() as 'en' | 'ar'\r\n    const t = useTranslations('homepage.cards')\r\n    const { formatPropertyPrice, isReady } = useCurrencyState()\r\n\r\n    const [currentImageIndex, setCurrentImageIndex] = useState(0)\r\n\r\n    const handleFavoriteClick = (e: React.MouseEvent) => {\r\n        e.preventDefault()\r\n        e.stopPropagation()\r\n\r\n        if (isFavoriteLoading || !onFavoriteToggle) return\r\n\r\n        onFavoriteToggle(property.id)\r\n    }\r\n\r\n    const handleCardClick = () => {\r\n        if (viewType === 'properties') {\r\n            router.push(`/properties/${property.slug}`)\r\n        } else {\r\n            router.push(`/persons/${property.slug}`)\r\n        }\r\n    }\r\n\r\n    // Get price period text based on rentTime or paymentTime\r\n    const getPricePeriod = () => {\r\n        // Use rentTime as primary, fallback to paymentTime, then default to annually\r\n        const period = property.rentTime || property.paymentTime || 'annually'\r\n\r\n        // Map the period to translation key\r\n        const periodMap: Record<string, string> = {\r\n            'daily': 'pricePeriods.daily',\r\n            'weekly': 'pricePeriods.weekly',\r\n            'monthly': 'pricePeriods.monthly',\r\n            'quarterly': 'pricePeriods.quarterly',\r\n            'semiannual': 'pricePeriods.semiannual',\r\n            'annually': 'pricePeriods.annually'\r\n        }\r\n\r\n        return t(periodMap[period] || 'pricePeriods.annually')\r\n    }\r\n\r\n    // Get gender icon and text\r\n    const getGenderDisplay = () => {\r\n        switch (property.genderRequired) {\r\n            case 'male':\r\n                return { icon: MaleIcon, text: 'Male' }\r\n            case 'female':\r\n                return { icon: FemaleIcon, text: 'Female' }\r\n            default:\r\n                return { icon: Users, text: 'Mixed' }\r\n        }\r\n    }\r\n\r\n    // Get country flag image for specific countries\r\n    const getCountryFlag = () => {\r\n        switch (property.country?.toUpperCase()) {\r\n            case 'UAE':\r\n            case 'AE':\r\n                return { url: 'https://flagcdn.com/w20/ae.png', alt: 'UAE Flag' }\r\n            case 'SAUDI ARABIA':\r\n            case 'SA':\r\n                return { url: 'https://flagcdn.com/w20/sa.png', alt: 'Saudi Arabia Flag' }\r\n            case 'EGYPT':\r\n            case 'EG':\r\n                return { url: 'https://flagcdn.com/w20/eg.png', alt: 'Egypt Flag' }\r\n            case 'JORDAN':\r\n            case 'JO':\r\n                return { url: 'https://flagcdn.com/w20/jo.png', alt: 'Jordan Flag' }\r\n            default:\r\n                return { url: 'https://flagcdn.com/w20/xx.png', alt: 'Default Flag' }\r\n        }\r\n    }\r\n\r\n    const genderDisplay = getGenderDisplay()\r\n    const countryFlag = getCountryFlag()\r\n\r\n    // Prepare images array with fallback\r\n    const images = property.images && property.images.length > 0\r\n        ? property.images\r\n        : ['/images/placeholder-property.jpg']\r\n\r\n    const goToPrevious = () => {\r\n        setCurrentImageIndex((prevIndex) =>\r\n            prevIndex === 0 ? images.length - 1 : prevIndex - 1\r\n        )\r\n    }\r\n\r\n    const goToNext = () => {\r\n        setCurrentImageIndex((prevIndex) =>\r\n            prevIndex === images.length - 1 ? 0 : prevIndex + 1\r\n        )\r\n    }\r\n\r\n    return (\r\n        <Card\r\n            className={cn(\r\n                \"group transition-all py-0 duration-200 hover:shadow-lg border-0 overflow-hidden bg-white dark:bg-card rounded-xl relative h-80\",\r\n                className\r\n            )}\r\n        >\r\n            <CardContent className=\"p-0 h-full relative\">\r\n                {/* Background Image Slider */}\r\n                <div\r\n                    className=\"absolute inset-0 w-full h-full transition-all duration-700 ease-out group-hover:scale-105 cursor-pointer\"\r\n                    style={{\r\n                        backgroundImage: `url(${images[currentImageIndex]})`,\r\n                        backgroundSize: 'cover',\r\n                        backgroundPosition: 'center',\r\n                        backgroundRepeat: 'no-repeat'\r\n                    }}\r\n                    onClick={handleCardClick}\r\n                >\r\n\r\n                </div>\r\n\r\n                {/* Navigation Arrows - Fixed Position Outside Scaled Container */}\r\n                {images.length > 1 && (\r\n                    <>\r\n                        <Button\r\n                            variant=\"secondary\"\r\n                            size=\"icon\"\r\n                            className=\"absolute left-3 top-1/2 -translate-y-1/2 opacity-80 hover:opacity-100 transition-opacity duration-300 bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm shadow-lg z-30 w-9 h-9\"\r\n                            onClick={(e) => {\r\n                                e.stopPropagation()\r\n                                goToPrevious()\r\n                            }}\r\n                        >\r\n                            <ChevronLeft className=\"h-5 w-5 drop-shadow-lg\" />\r\n                        </Button>\r\n                        <Button\r\n                            variant=\"secondary\"\r\n                            size=\"icon\"\r\n                            className=\"absolute right-3 top-1/2 -translate-y-1/2 opacity-80 hover:opacity-100 transition-opacity duration-300 bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm shadow-lg z-30 w-9 h-9\"\r\n                            onClick={(e) => {\r\n                                e.stopPropagation()\r\n                                goToNext()\r\n                            }}\r\n                        >\r\n                            <ChevronRight className=\"h-5 w-5 drop-shadow-lg\" />\r\n                        </Button>\r\n                    </>\r\n                )}\r\n\r\n                {/* Bottom Overlay for Text Readability */}\r\n                <div className=\"absolute bottom-0 left-0 right-0 h-48 bg-gradient-to-t from-black/80 to-transparent transition-all duration-200 group-hover:from-black/90\"></div>\r\n\r\n                {/* Top Section - Country Flag and Favorite Button */}\r\n                <div className=\"absolute top-0 left-0 right-0 flex justify-between items-start p-3 z-10\">\r\n                    {/* Country Flag */}\r\n                    <div className=\"w-8 h-8 rounded-full overflow-hidden border-2 border-white/80 shadow-lg flex items-center justify-center\">\r\n                        <img\r\n                            src={countryFlag.url}\r\n                            alt={countryFlag.alt}\r\n                            className=\"w-6 h-5 object-cover rounded-full\"\r\n                        />\r\n                    </div>\r\n\r\n                    {/* Favorite Button */}\r\n                    <Button\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        className={cn(\r\n                            \"bg-white/15 hover:bg-white/25 text-white hover:text-red-400 transition-all duration-300 w-10 h-10 rounded-full shadow-xl hover:shadow-2xl hover:scale-110 backdrop-blur-sm border border-white/20\",\r\n                            isFavorite && \"text-red-500 hover:text-red-400 bg-white/20 border-red-500/30\"\r\n                        )}\r\n                        onClick={handleFavoriteClick}\r\n                        disabled={isFavoriteLoading}\r\n                    >\r\n                        <Heart\r\n                            className={cn(\r\n                                \"h-6 w-6 transition-all duration-300 drop-shadow-lg\",\r\n                                isFavorite && \"fill-current scale-110\"\r\n                            )}\r\n                        />\r\n                    </Button>\r\n                </div>\r\n\r\n                {/* Bottom Section - Property Info */}\r\n                <div\r\n                    className=\"absolute bottom-0 left-0 right-0 p-4 text-white z-10 cursor-pointer\"\r\n                    onClick={handleCardClick}\r\n                >\r\n                    {/* Title and Price */}\r\n                    <div className=\"mb-3\">\r\n                        <h3 className=\"font-bold text-xl mb-2 line-clamp-2 leading-tight text-white drop-shadow-xl\">\r\n                            {property.title}\r\n                        </h3>\r\n                        <div className=\"flex items-baseline gap-2\">\r\n                            <div className=\"text-2xl font-extrabold text-white drop-shadow-2xl\">\r\n                                {isReady ? formatPropertyPrice(property.price, property.country, {\r\n                                    showSymbol: true,\r\n                                    locale: locale\r\n                                }) : `$${property.price}`}\r\n                            </div>\r\n                            <div className=\"text-sm font-semibold text-yellow-100 drop-shadow-lg\">\r\n                                {getPricePeriod()}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Separator Line */}\r\n                    <div className=\"w-full h-px bg-white/20 mb-3\"></div>\r\n\r\n                    {/* Property Details */}\r\n                    <div className=\"flex items-center justify-between text-sm\">\r\n                        {/* Size */}\r\n                        {property.size && (\r\n                            <div className=\"flex items-center gap-1.5\">\r\n                                <Square className=\"h-5 w-5 text-amber-300 drop-shadow-lg\" />\r\n                                <span className=\"text-white font-semibold drop-shadow-md\">{property.size} SqFT</span>\r\n                            </div>\r\n                        )}\r\n\r\n                        {/* Bathrooms */}\r\n                        {property.bathrooms && (\r\n                            <div className=\"flex items-center gap-1.5\">\r\n                                <Bath className=\"h-5 w-5 text-cyan-300 drop-shadow-lg\" />\r\n                                <span className=\"text-white font-semibold drop-shadow-md\">{property.bathrooms}</span>\r\n                            </div>\r\n                        )}\r\n\r\n                        {/* Bedrooms */}\r\n                        {property.totalRooms && (\r\n                            <div className=\"flex items-center gap-1.5\">\r\n                                <Bed className=\"h-5 w-5 text-green-300 drop-shadow-lg\" />\r\n                                <span className=\"text-white font-semibold drop-shadow-md\">{property.totalRooms}</span>\r\n                            </div>\r\n                        )}\r\n\r\n                        {/* Gender */}\r\n                        <div className=\"flex items-center gap-1.5\">\r\n                            <genderDisplay.icon className={cn(\r\n                                \"h-5 w-5 drop-shadow-lg\",\r\n                                property.genderRequired === 'male' && \"text-blue-300\",\r\n                                property.genderRequired === 'female' && \"text-pink-300\",\r\n                                property.genderRequired !== 'male' && property.genderRequired !== 'female' && \"text-purple-300\"\r\n                            )} />\r\n                            <span className=\"text-white font-semibold drop-shadow-md\">{genderDisplay.text}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </CardContent>\r\n        </Card>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAuEA,sBAAsB;AACtB,MAAM,WAAW,CAAC,EAAE,SAAS,EAA0B,iBACnD,6LAAC;QACG,WAAW;QACX,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;0BAC1B,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;KAZV;AAgBN,MAAM,aAAa,CAAC,EAAE,SAAS,EAA0B,iBACrD,6LAAC;QACG,WAAW;QACX,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;;;;;;0BACzB,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;MAZV;AAgBC,SAAS,aAAa,EAAE,QAAQ,EAAE,gBAAgB,EAAE,SAAS,EAAE,QAAQ,EAAE,oBAAoB,KAAK,EAAE,aAAa,KAAK,EAAqB;;IAC9I,MAAM,SAAS,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IAExD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,sBAAsB,CAAC;QACzB,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,qBAAqB,CAAC,kBAAkB;QAE5C,iBAAiB,SAAS,EAAE;IAChC;IAEA,MAAM,kBAAkB;QACpB,IAAI,aAAa,cAAc;YAC3B,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,IAAI,EAAE;QAC9C,OAAO;YACH,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,EAAE;QAC3C;IACJ;IAEA,yDAAyD;IACzD,MAAM,iBAAiB;QACnB,6EAA6E;QAC7E,MAAM,SAAS,SAAS,QAAQ,IAAI,SAAS,WAAW,IAAI;QAE5D,oCAAoC;QACpC,MAAM,YAAoC;YACtC,SAAS;YACT,UAAU;YACV,WAAW;YACX,aAAa;YACb,cAAc;YACd,YAAY;QAChB;QAEA,OAAO,EAAE,SAAS,CAAC,OAAO,IAAI;IAClC;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB;QACrB,OAAQ,SAAS,cAAc;YAC3B,KAAK;gBACD,OAAO;oBAAE,MAAM;oBAAU,MAAM;gBAAO;YAC1C,KAAK;gBACD,OAAO;oBAAE,MAAM;oBAAY,MAAM;gBAAS;YAC9C;gBACI,OAAO;oBAAE,MAAM,uMAAA,CAAA,QAAK;oBAAE,MAAM;gBAAQ;QAC5C;IACJ;IAEA,gDAAgD;IAChD,MAAM,iBAAiB;QACnB,OAAQ,SAAS,OAAO,EAAE;YACtB,KAAK;YACL,KAAK;gBACD,OAAO;oBAAE,KAAK;oBAAkC,KAAK;gBAAW;YACpE,KAAK;YACL,KAAK;gBACD,OAAO;oBAAE,KAAK;oBAAkC,KAAK;gBAAoB;YAC7E,KAAK;YACL,KAAK;gBACD,OAAO;oBAAE,KAAK;oBAAkC,KAAK;gBAAa;YACtE,KAAK;YACL,KAAK;gBACD,OAAO;oBAAE,KAAK;oBAAkC,KAAK;gBAAc;YACvE;gBACI,OAAO;oBAAE,KAAK;oBAAkC,KAAK;gBAAe;QAC5E;IACJ;IAEA,MAAM,gBAAgB;IACtB,MAAM,cAAc;IAEpB,qCAAqC;IACrC,MAAM,SAAS,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,IACrD,SAAS,MAAM,GACf;QAAC;KAAmC;IAE1C,MAAM,eAAe;QACjB,qBAAqB,CAAC,YAClB,cAAc,IAAI,OAAO,MAAM,GAAG,IAAI,YAAY;IAE1D;IAEA,MAAM,WAAW;QACb,qBAAqB,CAAC,YAClB,cAAc,OAAO,MAAM,GAAG,IAAI,IAAI,YAAY;IAE1D;IAEA,qBACI,6LAAC,mIAAA,CAAA,OAAI;QACD,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,kIACA;kBAGJ,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BAEnB,6LAAC;oBACG,WAAU;oBACV,OAAO;wBACH,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC;wBACpD,gBAAgB;wBAChB,oBAAoB;wBACpB,kBAAkB;oBACtB;oBACA,SAAS;;;;;;gBAMZ,OAAO,MAAM,GAAG,mBACb;;sCACI,6LAAC,qIAAA,CAAA,SAAM;4BACH,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,CAAC;gCACN,EAAE,eAAe;gCACjB;4BACJ;sCAEA,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAE3B,6LAAC,qIAAA,CAAA,SAAM;4BACH,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,CAAC;gCACN,EAAE,eAAe;gCACjB;4BACJ;sCAEA,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;8BAMpC,6LAAC;oBAAI,WAAU;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCAEX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCACG,KAAK,YAAY,GAAG;gCACpB,KAAK,YAAY,GAAG;gCACpB,WAAU;;;;;;;;;;;sCAKlB,6LAAC,qIAAA,CAAA,SAAM;4BACH,SAAQ;4BACR,MAAK;4BACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,qMACA,cAAc;4BAElB,SAAS;4BACT,UAAU;sCAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCACF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,sDACA,cAAc;;;;;;;;;;;;;;;;;8BAO9B,6LAAC;oBACG,WAAU;oBACV,SAAS;;sCAGT,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAG,WAAU;8CACT,SAAS,KAAK;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;sDACV,UAAU,oBAAoB,SAAS,KAAK,EAAE,SAAS,OAAO,EAAE;gDAC7D,YAAY;gDACZ,QAAQ;4CACZ,KAAK,CAAC,CAAC,EAAE,SAAS,KAAK,EAAE;;;;;;sDAE7B,6LAAC;4CAAI,WAAU;sDACV;;;;;;;;;;;;;;;;;;sCAMb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;gCAEV,SAAS,IAAI,kBACV,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;;gDAA2C,SAAS,IAAI;gDAAC;;;;;;;;;;;;;gCAKhF,SAAS,SAAS,kBACf,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAA2C,SAAS,SAAS;;;;;;;;;;;;gCAKpF,SAAS,UAAU,kBAChB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAA2C,SAAS,UAAU;;;;;;;;;;;;8CAKtF,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,cAAc,IAAI;4CAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAC5B,0BACA,SAAS,cAAc,KAAK,UAAU,iBACtC,SAAS,cAAc,KAAK,YAAY,iBACxC,SAAS,cAAc,KAAK,UAAU,SAAS,cAAc,KAAK,YAAY;;;;;;sDAElF,6LAAC;4CAAK,WAAU;sDAA2C,cAAc,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzG;GAtPgB;;QACG,4HAAA,CAAA,YAAS;QACT,qKAAA,CAAA,YAAS;QACd,yMAAA,CAAA,kBAAe;QACgB,mIAAA,CAAA,mBAAgB;;;MAJ7C", "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/properties.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { del, get, patch, post } from \"@/services/api\";\r\n\r\ninterface Property {\r\n  title: string;\r\n  images: string[];\r\n  city: string;\r\n  country: string;\r\n  neighborhood: string;\r\n  latitude: string;\r\n  longitude: string;\r\n  address: string;\r\n  description: string;\r\n  type: string;\r\n  roomType: string;\r\n  genderRequired: string;\r\n  totalRooms: string;\r\n  availableRooms: string;\r\n  price: string;\r\n  size: string;\r\n  floor: string;\r\n  bathrooms: string;\r\n  separatedBathroom: boolean;\r\n  residentsCount: string;\r\n  availablePersons: string;\r\n  rentTime: string;\r\n  paymentTime: string;\r\n  priceIncludeWaterAndElectricity: boolean;\r\n  allowSmoking: boolean;\r\n  includeFurniture: boolean;\r\n  airConditioning: boolean;\r\n  includeWaterHeater: boolean;\r\n  parking: boolean;\r\n  internet: boolean;\r\n  nearToMetro: boolean;\r\n  nearToMarket: boolean;\r\n  elevator: boolean;\r\n  trialPeriod: boolean;\r\n  goodForForeigners: boolean;\r\n  categoryId: string;\r\n  termsAndConditions: string;\r\n}\r\n\r\nexport const createProperty = async (data: Property) => {\r\n  console.log(\"create property data\", data);\r\n  const response = await post(\"/properties\", data);\r\n  console.log(\"create property response\", response);\r\n  return response;\r\n};\r\n\r\nexport const getProperties = async ({\r\n  page = 1,\r\n  limit = 12,\r\n  title = \"\",\r\n  city = \"\",\r\n  country = \"\",\r\n  neighborhood = \"\",\r\n  address = \"\",\r\n  type = \"\",\r\n  roomType = \"\",\r\n  genderRequired = \"\",\r\n  totalRooms = \"\",\r\n  availableRooms = \"\",\r\n  size = \"\",\r\n  floor = \"\",\r\n  bathrooms = \"\",\r\n  separatedBathroom = false,\r\n  residentsCount = \"\",\r\n  availablePersons = \"\",\r\n  minPrice = \"\",\r\n  maxPrice = \"\",\r\n  rentTime = \"\",\r\n  paymentTime = \"\",\r\n  priceIncludeWaterAndElectricity = false,\r\n  minRating = 0,\r\n  minTotalRatings = 0,\r\n  isVerified = false,\r\n  isAvailable = false,\r\n  categoryId = \"\",\r\n  allowSmoking = false,\r\n  includeFurniture = false,\r\n  airConditioning = false,\r\n  includeWaterHeater = false,\r\n  parking = false,\r\n  internet = false,\r\n  nearToMetro = false,\r\n  nearToMarket = false,\r\n  elevator = false,\r\n  trialPeriod = false,\r\n  goodForForeigners = false,\r\n  sortBy = \"createdAt\",\r\n  sortOrder = \"asc\",\r\n}: {\r\n  page?: number;\r\n  limit?: number;\r\n  title?: string;\r\n  city?: string;\r\n  country?: string;\r\n  neighborhood?: string;\r\n  address?: string;\r\n  latitude?: string;\r\n  longitude?: string;\r\n  type?: string;\r\n  roomType?: string;\r\n  genderRequired?: string;\r\n  totalRooms?: string;\r\n  availableRooms?: string;\r\n  size?: string;\r\n  floor?: string;\r\n  bathrooms?: string;\r\n  separatedBathroom?: boolean;\r\n  residentsCount?: string;\r\n  availablePersons?: string;\r\n  minPrice?: string;\r\n  maxPrice?: string;\r\n  rentTime?: string;\r\n  paymentTime?: string;\r\n  priceIncludeWaterAndElectricity?: boolean;\r\n  minRating?: number;\r\n  minTotalRatings?: number;\r\n  isVerified?: boolean;\r\n  isAvailable?: boolean;\r\n  categoryId?: string;\r\n  allowSmoking?: boolean;\r\n  includeFurniture?: boolean;\r\n  airConditioning?: boolean;\r\n  includeWaterHeater?: boolean;\r\n  parking?: boolean;\r\n  internet?: boolean;\r\n  nearToMetro?: boolean;\r\n  nearToMarket?: boolean;\r\n  elevator?: boolean;\r\n  trialPeriod?: boolean;\r\n  goodForForeigners?: boolean;\r\n  sortBy?: \"createdAt\" | \"updatedAt\" | \"price\" | \"rating\" | \"title\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n}) => {\r\n  const response = await get(\r\n    `/properties?page=${page}&limit=${limit}&${title ? `title=${title}&` : \"\"}${\r\n      city ? `city=${city}&` : \"\"\r\n    }${country ? `country=${country}&` : \"\"}${\r\n      neighborhood ? `neighborhood=${neighborhood}&` : \"\"\r\n    }${address ? `address=${address}&` : \"\"}${type ? `type=${type}&` : \"\"}${\r\n      roomType ? `roomType=${roomType}&` : \"\"\r\n    }${genderRequired ? `genderRequired=${genderRequired}&` : \"\"}${\r\n      totalRooms ? `totalRooms=${totalRooms}&` : \"\"\r\n    }${availableRooms ? `availableRooms=${availableRooms}&` : \"\"}${\r\n      size ? `size=${size}&` : \"\"\r\n    }${floor ? `floor=${floor}&` : \"\"}${\r\n      bathrooms ? `bathrooms=${bathrooms}&` : \"\"\r\n    }${separatedBathroom ? `separatedBathroom=${separatedBathroom}&` : \"\"}${\r\n      residentsCount ? `residentsCount=${residentsCount}&` : \"\"\r\n    }${availablePersons ? `availablePersons=${availablePersons}&` : \"\"}${\r\n      rentTime ? `rentTime=${rentTime}&` : \"\"\r\n    }${paymentTime ? `paymentTime=${paymentTime}&` : \"\"}${\r\n      priceIncludeWaterAndElectricity\r\n        ? `priceIncludeWaterAndElectricity=${priceIncludeWaterAndElectricity}&`\r\n        : \"\"\r\n    }${allowSmoking ? `allowSmoking=${allowSmoking}&` : \"\"}${\r\n      includeFurniture ? `includeFurniture=${includeFurniture}&` : \"\"\r\n    }${airConditioning ? `airConditioning=${airConditioning}&` : \"\"}${\r\n      includeWaterHeater ? `includeWaterHeater=${includeWaterHeater}&` : \"\"\r\n    }${parking ? `parking=${parking}&` : \"\"}${\r\n      internet ? `internet=${internet}&` : \"\"\r\n    }${nearToMetro ? `nearToMetro=${nearToMetro}&` : \"\"}${\r\n      nearToMarket ? `nearToMarket=${nearToMarket}&` : \"\"\r\n    }${elevator ? `elevator=${elevator}&` : \"\"}${\r\n      trialPeriod ? `trialPeriod=${trialPeriod}&` : \"\"\r\n    }${goodForForeigners ? `goodForForeigners=${goodForForeigners}&` : \"\"}${\r\n      categoryId ? `categoryId=${categoryId}&` : \"\"\r\n    }${minRating ? `minRating=${minRating}&` : \"\"}${\r\n      minTotalRatings ? `minTotalRatings=${minTotalRatings}&` : \"\"\r\n    }${isVerified ? `isVerified=${isVerified}&` : \"\"}${\r\n      minPrice ? `minPrice=${minPrice}&` : \"\"\r\n    }${maxPrice ? `maxPrice=${maxPrice}&` : \"\"}${\r\n      isAvailable ? `isAvailable=${isAvailable}&` : \"\"\r\n    }${sortBy ? `sortBy=${sortBy}&` : \"\"}${\r\n      sortOrder ? `sortOrder=${sortOrder}` : \"\"\r\n    }`\r\n  );\r\n\r\n  return response;\r\n};\r\n\r\nexport const getMyProperties = async () => {\r\n  const response = await get(`/properties/my-properties`);\r\n  return response;\r\n};\r\n\r\nexport const getProperty = async (slug: string) => {\r\n  const response = await get(`/properties/slug/${slug}`);\r\n  return response;\r\n};\r\n\r\nexport const getPropertiesByUserId = async (userId: string) => {\r\n  const response = await get(`/properties/user/${userId}`);\r\n  return response;\r\n};\r\n\r\nexport const getFavoriteProperties = async () => {\r\n  const response = await get(`/properties/favorites`);\r\n  return response;\r\n};\r\n\r\nexport const toggleFavorite = async (id: string) => {\r\n  const response = await patch(`/properties/${id}/toggle-favorite`);\r\n  console.log(\"toggle favorite response\", response);\r\n  return response;\r\n};\r\n\r\nexport const updateProperty = async (data: Property) => {\r\n  const response = await patch(\"/properties\", data);\r\n  return response;\r\n};\r\n\r\nexport const deleteProperty = async (id: string) => {\r\n  const response = await del(`/properties/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAyLa,kBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1274, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/properties.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { del, get, patch, post } from \"@/services/api\";\r\n\r\ninterface Property {\r\n  title: string;\r\n  images: string[];\r\n  city: string;\r\n  country: string;\r\n  neighborhood: string;\r\n  latitude: string;\r\n  longitude: string;\r\n  address: string;\r\n  description: string;\r\n  type: string;\r\n  roomType: string;\r\n  genderRequired: string;\r\n  totalRooms: string;\r\n  availableRooms: string;\r\n  price: string;\r\n  size: string;\r\n  floor: string;\r\n  bathrooms: string;\r\n  separatedBathroom: boolean;\r\n  residentsCount: string;\r\n  availablePersons: string;\r\n  rentTime: string;\r\n  paymentTime: string;\r\n  priceIncludeWaterAndElectricity: boolean;\r\n  allowSmoking: boolean;\r\n  includeFurniture: boolean;\r\n  airConditioning: boolean;\r\n  includeWaterHeater: boolean;\r\n  parking: boolean;\r\n  internet: boolean;\r\n  nearToMetro: boolean;\r\n  nearToMarket: boolean;\r\n  elevator: boolean;\r\n  trialPeriod: boolean;\r\n  goodForForeigners: boolean;\r\n  categoryId: string;\r\n  termsAndConditions: string;\r\n}\r\n\r\nexport const createProperty = async (data: Property) => {\r\n  console.log(\"create property data\", data);\r\n  const response = await post(\"/properties\", data);\r\n  console.log(\"create property response\", response);\r\n  return response;\r\n};\r\n\r\nexport const getProperties = async ({\r\n  page = 1,\r\n  limit = 12,\r\n  title = \"\",\r\n  city = \"\",\r\n  country = \"\",\r\n  neighborhood = \"\",\r\n  address = \"\",\r\n  type = \"\",\r\n  roomType = \"\",\r\n  genderRequired = \"\",\r\n  totalRooms = \"\",\r\n  availableRooms = \"\",\r\n  size = \"\",\r\n  floor = \"\",\r\n  bathrooms = \"\",\r\n  separatedBathroom = false,\r\n  residentsCount = \"\",\r\n  availablePersons = \"\",\r\n  minPrice = \"\",\r\n  maxPrice = \"\",\r\n  rentTime = \"\",\r\n  paymentTime = \"\",\r\n  priceIncludeWaterAndElectricity = false,\r\n  minRating = 0,\r\n  minTotalRatings = 0,\r\n  isVerified = false,\r\n  isAvailable = false,\r\n  categoryId = \"\",\r\n  allowSmoking = false,\r\n  includeFurniture = false,\r\n  airConditioning = false,\r\n  includeWaterHeater = false,\r\n  parking = false,\r\n  internet = false,\r\n  nearToMetro = false,\r\n  nearToMarket = false,\r\n  elevator = false,\r\n  trialPeriod = false,\r\n  goodForForeigners = false,\r\n  sortBy = \"createdAt\",\r\n  sortOrder = \"asc\",\r\n}: {\r\n  page?: number;\r\n  limit?: number;\r\n  title?: string;\r\n  city?: string;\r\n  country?: string;\r\n  neighborhood?: string;\r\n  address?: string;\r\n  latitude?: string;\r\n  longitude?: string;\r\n  type?: string;\r\n  roomType?: string;\r\n  genderRequired?: string;\r\n  totalRooms?: string;\r\n  availableRooms?: string;\r\n  size?: string;\r\n  floor?: string;\r\n  bathrooms?: string;\r\n  separatedBathroom?: boolean;\r\n  residentsCount?: string;\r\n  availablePersons?: string;\r\n  minPrice?: string;\r\n  maxPrice?: string;\r\n  rentTime?: string;\r\n  paymentTime?: string;\r\n  priceIncludeWaterAndElectricity?: boolean;\r\n  minRating?: number;\r\n  minTotalRatings?: number;\r\n  isVerified?: boolean;\r\n  isAvailable?: boolean;\r\n  categoryId?: string;\r\n  allowSmoking?: boolean;\r\n  includeFurniture?: boolean;\r\n  airConditioning?: boolean;\r\n  includeWaterHeater?: boolean;\r\n  parking?: boolean;\r\n  internet?: boolean;\r\n  nearToMetro?: boolean;\r\n  nearToMarket?: boolean;\r\n  elevator?: boolean;\r\n  trialPeriod?: boolean;\r\n  goodForForeigners?: boolean;\r\n  sortBy?: \"createdAt\" | \"updatedAt\" | \"price\" | \"rating\" | \"title\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n}) => {\r\n  const response = await get(\r\n    `/properties?page=${page}&limit=${limit}&${title ? `title=${title}&` : \"\"}${\r\n      city ? `city=${city}&` : \"\"\r\n    }${country ? `country=${country}&` : \"\"}${\r\n      neighborhood ? `neighborhood=${neighborhood}&` : \"\"\r\n    }${address ? `address=${address}&` : \"\"}${type ? `type=${type}&` : \"\"}${\r\n      roomType ? `roomType=${roomType}&` : \"\"\r\n    }${genderRequired ? `genderRequired=${genderRequired}&` : \"\"}${\r\n      totalRooms ? `totalRooms=${totalRooms}&` : \"\"\r\n    }${availableRooms ? `availableRooms=${availableRooms}&` : \"\"}${\r\n      size ? `size=${size}&` : \"\"\r\n    }${floor ? `floor=${floor}&` : \"\"}${\r\n      bathrooms ? `bathrooms=${bathrooms}&` : \"\"\r\n    }${separatedBathroom ? `separatedBathroom=${separatedBathroom}&` : \"\"}${\r\n      residentsCount ? `residentsCount=${residentsCount}&` : \"\"\r\n    }${availablePersons ? `availablePersons=${availablePersons}&` : \"\"}${\r\n      rentTime ? `rentTime=${rentTime}&` : \"\"\r\n    }${paymentTime ? `paymentTime=${paymentTime}&` : \"\"}${\r\n      priceIncludeWaterAndElectricity\r\n        ? `priceIncludeWaterAndElectricity=${priceIncludeWaterAndElectricity}&`\r\n        : \"\"\r\n    }${allowSmoking ? `allowSmoking=${allowSmoking}&` : \"\"}${\r\n      includeFurniture ? `includeFurniture=${includeFurniture}&` : \"\"\r\n    }${airConditioning ? `airConditioning=${airConditioning}&` : \"\"}${\r\n      includeWaterHeater ? `includeWaterHeater=${includeWaterHeater}&` : \"\"\r\n    }${parking ? `parking=${parking}&` : \"\"}${\r\n      internet ? `internet=${internet}&` : \"\"\r\n    }${nearToMetro ? `nearToMetro=${nearToMetro}&` : \"\"}${\r\n      nearToMarket ? `nearToMarket=${nearToMarket}&` : \"\"\r\n    }${elevator ? `elevator=${elevator}&` : \"\"}${\r\n      trialPeriod ? `trialPeriod=${trialPeriod}&` : \"\"\r\n    }${goodForForeigners ? `goodForForeigners=${goodForForeigners}&` : \"\"}${\r\n      categoryId ? `categoryId=${categoryId}&` : \"\"\r\n    }${minRating ? `minRating=${minRating}&` : \"\"}${\r\n      minTotalRatings ? `minTotalRatings=${minTotalRatings}&` : \"\"\r\n    }${isVerified ? `isVerified=${isVerified}&` : \"\"}${\r\n      minPrice ? `minPrice=${minPrice}&` : \"\"\r\n    }${maxPrice ? `maxPrice=${maxPrice}&` : \"\"}${\r\n      isAvailable ? `isAvailable=${isAvailable}&` : \"\"\r\n    }${sortBy ? `sortBy=${sortBy}&` : \"\"}${\r\n      sortOrder ? `sortOrder=${sortOrder}` : \"\"\r\n    }`\r\n  );\r\n\r\n  return response;\r\n};\r\n\r\nexport const getMyProperties = async () => {\r\n  const response = await get(`/properties/my-properties`);\r\n  return response;\r\n};\r\n\r\nexport const getProperty = async (slug: string) => {\r\n  const response = await get(`/properties/slug/${slug}`);\r\n  return response;\r\n};\r\n\r\nexport const getPropertiesByUserId = async (userId: string) => {\r\n  const response = await get(`/properties/user/${userId}`);\r\n  return response;\r\n};\r\n\r\nexport const getFavoriteProperties = async () => {\r\n  const response = await get(`/properties/favorites`);\r\n  return response;\r\n};\r\n\r\nexport const toggleFavorite = async (id: string) => {\r\n  const response = await patch(`/properties/${id}/toggle-favorite`);\r\n  console.log(\"toggle favorite response\", response);\r\n  return response;\r\n};\r\n\r\nexport const updateProperty = async (data: Property) => {\r\n  const response = await patch(\"/properties\", data);\r\n  return response;\r\n};\r\n\r\nexport const deleteProperty = async (id: string) => {\r\n  const response = await del(`/properties/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IA6Ma,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/persons.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { del, get, patch, post } from \"@/services/api\";\r\n\r\ninterface Person {\r\n  title: string;\r\n  images: string[];\r\n  city: string;\r\n  country: string;\r\n  neighborhood: string;\r\n  latitude: string;\r\n  longitude: string;\r\n  address: string;\r\n  description: string;\r\n  type: string;\r\n  roomType: string;\r\n  genderRequired: string;\r\n  totalRooms: string;\r\n  availableRooms: string;\r\n  price: string;\r\n  size: string;\r\n  floor: string;\r\n  bathrooms: string;\r\n  separatedBathroom: boolean;\r\n  residentsCount: string;\r\n  availablePersons: string;\r\n  rentTime: string;\r\n  paymentTime: string;\r\n  priceIncludeWaterAndElectricity: boolean;\r\n  allowSmoking: boolean;\r\n  includeFurniture: boolean;\r\n  airConditioning: boolean;\r\n  includeWaterHeater: boolean;\r\n  parking: boolean;\r\n  internet: boolean;\r\n  nearToMetro: boolean;\r\n  nearToMarket: boolean;\r\n  elevator: boolean;\r\n  trialPeriod: boolean;\r\n  goodForForeigners: boolean;\r\n  categoryId: string;\r\n  termsAndConditions: string;\r\n}\r\n\r\nexport const createPerson = async (data: Person) => {\r\n  const response = await post(\"/persons\", data);\r\n  return response;\r\n};\r\n\r\nexport const getPersons = async ({\r\n  page = 1,\r\n  limit = 12,\r\n  title = \"\",\r\n  city = \"\",\r\n  country = \"\",\r\n  neighborhood = \"\",\r\n  address = \"\",\r\n  type = \"\",\r\n  roomType = \"\",\r\n  genderRequired = \"\",\r\n  totalRooms = \"\",\r\n  availableRooms = \"\",\r\n  size = \"\",\r\n  floor = \"\",\r\n  bathrooms = \"\",\r\n  separatedBathroom = false,\r\n  residentsCount = \"\",\r\n  availablePersons = \"\",\r\n  minPrice = \"\",\r\n  maxPrice = \"\",\r\n  rentTime = \"\",\r\n  paymentTime = \"\",\r\n  priceIncludeWaterAndElectricity = false,\r\n  minRating = 0,\r\n  minTotalRatings = 0,\r\n  isVerified = false,\r\n  isAvailable = false,\r\n  categoryId = \"\",\r\n  allowSmoking = false,\r\n  includeFurniture = false,\r\n  airConditioning = false,\r\n  includeWaterHeater = false,\r\n  parking = false,\r\n  internet = false,\r\n  nearToMetro = false,\r\n  nearToMarket = false,\r\n  elevator = false,\r\n  trialPeriod = false,\r\n  goodForForeigners = false,\r\n  sortBy = \"createdAt\",\r\n  sortOrder = \"asc\",\r\n}: {\r\n  page?: number;\r\n  limit?: number;\r\n  title?: string;\r\n  city?: string;\r\n  country?: string;\r\n  neighborhood?: string;\r\n  address?: string;\r\n  latitude?: string;\r\n  longitude?: string;\r\n  type?: string;\r\n  roomType?: string;\r\n  genderRequired?: string;\r\n  totalRooms?: string;\r\n  availableRooms?: string;\r\n  size?: string;\r\n  floor?: string;\r\n  bathrooms?: string;\r\n  separatedBathroom?: boolean;\r\n  residentsCount?: string;\r\n  availablePersons?: string;\r\n  minPrice?: string;\r\n  maxPrice?: string;\r\n  rentTime?: string;\r\n  paymentTime?: string;\r\n  priceIncludeWaterAndElectricity?: boolean;\r\n  minRating?: number;\r\n  minTotalRatings?: number;\r\n  isVerified?: boolean;\r\n  isAvailable?: boolean;\r\n  categoryId?: string;\r\n  allowSmoking?: boolean;\r\n  includeFurniture?: boolean;\r\n  airConditioning?: boolean;\r\n  includeWaterHeater?: boolean;\r\n  parking?: boolean;\r\n  internet?: boolean;\r\n  nearToMetro?: boolean;\r\n  nearToMarket?: boolean;\r\n  elevator?: boolean;\r\n  trialPeriod?: boolean;\r\n  goodForForeigners?: boolean;\r\n  sortBy?: \"createdAt\" | \"updatedAt\" | \"price\" | \"rating\" | \"title\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n}) => {\r\n  const response = await get(\r\n    `/persons?page=${page}&limit=${limit}&${title ? `title=${title}&` : \"\"}${\r\n      city ? `city=${city}&` : \"\"\r\n    }${country ? `country=${country}&` : \"\"}${\r\n      neighborhood ? `neighborhood=${neighborhood}&` : \"\"\r\n    }${address ? `address=${address}&` : \"\"}${type ? `type=${type}&` : \"\"}${\r\n      roomType ? `roomType=${roomType}&` : \"\"\r\n    }${genderRequired ? `genderRequired=${genderRequired}&` : \"\"}${\r\n      totalRooms ? `totalRooms=${totalRooms}&` : \"\"\r\n    }${availableRooms ? `availableRooms=${availableRooms}&` : \"\"}${\r\n      size ? `size=${size}&` : \"\"\r\n    }${floor ? `floor=${floor}&` : \"\"}${\r\n      bathrooms ? `bathrooms=${bathrooms}&` : \"\"\r\n    }${separatedBathroom ? `separatedBathroom=${separatedBathroom}&` : \"\"}${\r\n      residentsCount ? `residentsCount=${residentsCount}&` : \"\"\r\n    }${availablePersons ? `availablePersons=${availablePersons}&` : \"\"}${\r\n      rentTime ? `rentTime=${rentTime}&` : \"\"\r\n    }${paymentTime ? `paymentTime=${paymentTime}&` : \"\"}${\r\n      priceIncludeWaterAndElectricity\r\n        ? `priceIncludeWaterAndElectricity=${priceIncludeWaterAndElectricity}&`\r\n        : \"\"\r\n    }${allowSmoking ? `allowSmoking=${allowSmoking}&` : \"\"}${\r\n      includeFurniture ? `includeFurniture=${includeFurniture}&` : \"\"\r\n    }${airConditioning ? `airConditioning=${airConditioning}&` : \"\"}${\r\n      includeWaterHeater ? `includeWaterHeater=${includeWaterHeater}&` : \"\"\r\n    }${parking ? `parking=${parking}&` : \"\"}${\r\n      internet ? `internet=${internet}&` : \"\"\r\n    }${nearToMetro ? `nearToMetro=${nearToMetro}&` : \"\"}${\r\n      nearToMarket ? `nearToMarket=${nearToMarket}&` : \"\"\r\n    }${elevator ? `elevator=${elevator}&` : \"\"}${\r\n      trialPeriod ? `trialPeriod=${trialPeriod}&` : \"\"\r\n    }${goodForForeigners ? `goodForForeigners=${goodForForeigners}&` : \"\"}${\r\n      categoryId ? `categoryId=${categoryId}&` : \"\"\r\n    }${minRating ? `minRating=${minRating}&` : \"\"}${\r\n      minTotalRatings ? `minTotalRatings=${minTotalRatings}&` : \"\"\r\n    }${isVerified ? `isVerified=${isVerified}&` : \"\"}${\r\n      isAvailable ? `isAvailable=${isAvailable}&` : \"\"\r\n    }${minPrice ? `minPrice=${minPrice}&` : \"\"}${\r\n      maxPrice ? `maxPrice=${maxPrice}&` : \"\"\r\n    }${sortBy ? `sortBy=${sortBy}&` : \"\"}${\r\n      sortOrder ? `sortOrder=${sortOrder}` : \"\"\r\n    }`\r\n  );\r\n  return response;\r\n};\r\n\r\nexport const getMyPersons = async () => {\r\n  const response = await get(`/persons/my-persons`);\r\n  return response;\r\n};\r\n\r\nexport const getPerson = async (slug: string) => {\r\n  const response = await get(`/persons/slug/${slug}`);\r\n  return response;\r\n};\r\n\r\nexport const getPersonsByUserId = async (userId: string) => {\r\n  const response = await get(`/persons/user/${userId}`);\r\n  return response;\r\n};\r\n\r\nexport const getFavoritePersons = async () => {\r\n  const response = await get(`/persons/favorites`);\r\n  return response;\r\n};\r\n\r\nexport const toggleFavorite = async (id: string) => {\r\n  const response = await patch(`/persons/${id}/toggle-favorite`);\r\n  return response;\r\n};\r\n\r\nexport const updatePerson = async (data: Person) => {\r\n  const response = await patch(\"/persons\", data);\r\n  return response;\r\n};\r\n\r\nexport const deletePerson = async (id: string) => {\r\n  const response = await del(`/persons/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAsLa,eAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/persons.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { del, get, patch, post } from \"@/services/api\";\r\n\r\ninterface Person {\r\n  title: string;\r\n  images: string[];\r\n  city: string;\r\n  country: string;\r\n  neighborhood: string;\r\n  latitude: string;\r\n  longitude: string;\r\n  address: string;\r\n  description: string;\r\n  type: string;\r\n  roomType: string;\r\n  genderRequired: string;\r\n  totalRooms: string;\r\n  availableRooms: string;\r\n  price: string;\r\n  size: string;\r\n  floor: string;\r\n  bathrooms: string;\r\n  separatedBathroom: boolean;\r\n  residentsCount: string;\r\n  availablePersons: string;\r\n  rentTime: string;\r\n  paymentTime: string;\r\n  priceIncludeWaterAndElectricity: boolean;\r\n  allowSmoking: boolean;\r\n  includeFurniture: boolean;\r\n  airConditioning: boolean;\r\n  includeWaterHeater: boolean;\r\n  parking: boolean;\r\n  internet: boolean;\r\n  nearToMetro: boolean;\r\n  nearToMarket: boolean;\r\n  elevator: boolean;\r\n  trialPeriod: boolean;\r\n  goodForForeigners: boolean;\r\n  categoryId: string;\r\n  termsAndConditions: string;\r\n}\r\n\r\nexport const createPerson = async (data: Person) => {\r\n  const response = await post(\"/persons\", data);\r\n  return response;\r\n};\r\n\r\nexport const getPersons = async ({\r\n  page = 1,\r\n  limit = 12,\r\n  title = \"\",\r\n  city = \"\",\r\n  country = \"\",\r\n  neighborhood = \"\",\r\n  address = \"\",\r\n  type = \"\",\r\n  roomType = \"\",\r\n  genderRequired = \"\",\r\n  totalRooms = \"\",\r\n  availableRooms = \"\",\r\n  size = \"\",\r\n  floor = \"\",\r\n  bathrooms = \"\",\r\n  separatedBathroom = false,\r\n  residentsCount = \"\",\r\n  availablePersons = \"\",\r\n  minPrice = \"\",\r\n  maxPrice = \"\",\r\n  rentTime = \"\",\r\n  paymentTime = \"\",\r\n  priceIncludeWaterAndElectricity = false,\r\n  minRating = 0,\r\n  minTotalRatings = 0,\r\n  isVerified = false,\r\n  isAvailable = false,\r\n  categoryId = \"\",\r\n  allowSmoking = false,\r\n  includeFurniture = false,\r\n  airConditioning = false,\r\n  includeWaterHeater = false,\r\n  parking = false,\r\n  internet = false,\r\n  nearToMetro = false,\r\n  nearToMarket = false,\r\n  elevator = false,\r\n  trialPeriod = false,\r\n  goodForForeigners = false,\r\n  sortBy = \"createdAt\",\r\n  sortOrder = \"asc\",\r\n}: {\r\n  page?: number;\r\n  limit?: number;\r\n  title?: string;\r\n  city?: string;\r\n  country?: string;\r\n  neighborhood?: string;\r\n  address?: string;\r\n  latitude?: string;\r\n  longitude?: string;\r\n  type?: string;\r\n  roomType?: string;\r\n  genderRequired?: string;\r\n  totalRooms?: string;\r\n  availableRooms?: string;\r\n  size?: string;\r\n  floor?: string;\r\n  bathrooms?: string;\r\n  separatedBathroom?: boolean;\r\n  residentsCount?: string;\r\n  availablePersons?: string;\r\n  minPrice?: string;\r\n  maxPrice?: string;\r\n  rentTime?: string;\r\n  paymentTime?: string;\r\n  priceIncludeWaterAndElectricity?: boolean;\r\n  minRating?: number;\r\n  minTotalRatings?: number;\r\n  isVerified?: boolean;\r\n  isAvailable?: boolean;\r\n  categoryId?: string;\r\n  allowSmoking?: boolean;\r\n  includeFurniture?: boolean;\r\n  airConditioning?: boolean;\r\n  includeWaterHeater?: boolean;\r\n  parking?: boolean;\r\n  internet?: boolean;\r\n  nearToMetro?: boolean;\r\n  nearToMarket?: boolean;\r\n  elevator?: boolean;\r\n  trialPeriod?: boolean;\r\n  goodForForeigners?: boolean;\r\n  sortBy?: \"createdAt\" | \"updatedAt\" | \"price\" | \"rating\" | \"title\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n}) => {\r\n  const response = await get(\r\n    `/persons?page=${page}&limit=${limit}&${title ? `title=${title}&` : \"\"}${\r\n      city ? `city=${city}&` : \"\"\r\n    }${country ? `country=${country}&` : \"\"}${\r\n      neighborhood ? `neighborhood=${neighborhood}&` : \"\"\r\n    }${address ? `address=${address}&` : \"\"}${type ? `type=${type}&` : \"\"}${\r\n      roomType ? `roomType=${roomType}&` : \"\"\r\n    }${genderRequired ? `genderRequired=${genderRequired}&` : \"\"}${\r\n      totalRooms ? `totalRooms=${totalRooms}&` : \"\"\r\n    }${availableRooms ? `availableRooms=${availableRooms}&` : \"\"}${\r\n      size ? `size=${size}&` : \"\"\r\n    }${floor ? `floor=${floor}&` : \"\"}${\r\n      bathrooms ? `bathrooms=${bathrooms}&` : \"\"\r\n    }${separatedBathroom ? `separatedBathroom=${separatedBathroom}&` : \"\"}${\r\n      residentsCount ? `residentsCount=${residentsCount}&` : \"\"\r\n    }${availablePersons ? `availablePersons=${availablePersons}&` : \"\"}${\r\n      rentTime ? `rentTime=${rentTime}&` : \"\"\r\n    }${paymentTime ? `paymentTime=${paymentTime}&` : \"\"}${\r\n      priceIncludeWaterAndElectricity\r\n        ? `priceIncludeWaterAndElectricity=${priceIncludeWaterAndElectricity}&`\r\n        : \"\"\r\n    }${allowSmoking ? `allowSmoking=${allowSmoking}&` : \"\"}${\r\n      includeFurniture ? `includeFurniture=${includeFurniture}&` : \"\"\r\n    }${airConditioning ? `airConditioning=${airConditioning}&` : \"\"}${\r\n      includeWaterHeater ? `includeWaterHeater=${includeWaterHeater}&` : \"\"\r\n    }${parking ? `parking=${parking}&` : \"\"}${\r\n      internet ? `internet=${internet}&` : \"\"\r\n    }${nearToMetro ? `nearToMetro=${nearToMetro}&` : \"\"}${\r\n      nearToMarket ? `nearToMarket=${nearToMarket}&` : \"\"\r\n    }${elevator ? `elevator=${elevator}&` : \"\"}${\r\n      trialPeriod ? `trialPeriod=${trialPeriod}&` : \"\"\r\n    }${goodForForeigners ? `goodForForeigners=${goodForForeigners}&` : \"\"}${\r\n      categoryId ? `categoryId=${categoryId}&` : \"\"\r\n    }${minRating ? `minRating=${minRating}&` : \"\"}${\r\n      minTotalRatings ? `minTotalRatings=${minTotalRatings}&` : \"\"\r\n    }${isVerified ? `isVerified=${isVerified}&` : \"\"}${\r\n      isAvailable ? `isAvailable=${isAvailable}&` : \"\"\r\n    }${minPrice ? `minPrice=${minPrice}&` : \"\"}${\r\n      maxPrice ? `maxPrice=${maxPrice}&` : \"\"\r\n    }${sortBy ? `sortBy=${sortBy}&` : \"\"}${\r\n      sortOrder ? `sortOrder=${sortOrder}` : \"\"\r\n    }`\r\n  );\r\n  return response;\r\n};\r\n\r\nexport const getMyPersons = async () => {\r\n  const response = await get(`/persons/my-persons`);\r\n  return response;\r\n};\r\n\r\nexport const getPerson = async (slug: string) => {\r\n  const response = await get(`/persons/slug/${slug}`);\r\n  return response;\r\n};\r\n\r\nexport const getPersonsByUserId = async (userId: string) => {\r\n  const response = await get(`/persons/user/${userId}`);\r\n  return response;\r\n};\r\n\r\nexport const getFavoritePersons = async () => {\r\n  const response = await get(`/persons/favorites`);\r\n  return response;\r\n};\r\n\r\nexport const toggleFavorite = async (id: string) => {\r\n  const response = await patch(`/persons/${id}/toggle-favorite`);\r\n  return response;\r\n};\r\n\r\nexport const updatePerson = async (data: Person) => {\r\n  const response = await patch(\"/persons\", data);\r\n  return response;\r\n};\r\n\r\nexport const deletePerson = async (id: string) => {\r\n  const response = await del(`/persons/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IA0Ma,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/properties.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { del, get, patch, post } from \"@/services/api\";\r\n\r\ninterface Property {\r\n  title: string;\r\n  images: string[];\r\n  city: string;\r\n  country: string;\r\n  neighborhood: string;\r\n  latitude: string;\r\n  longitude: string;\r\n  address: string;\r\n  description: string;\r\n  type: string;\r\n  roomType: string;\r\n  genderRequired: string;\r\n  totalRooms: string;\r\n  availableRooms: string;\r\n  price: string;\r\n  size: string;\r\n  floor: string;\r\n  bathrooms: string;\r\n  separatedBathroom: boolean;\r\n  residentsCount: string;\r\n  availablePersons: string;\r\n  rentTime: string;\r\n  paymentTime: string;\r\n  priceIncludeWaterAndElectricity: boolean;\r\n  allowSmoking: boolean;\r\n  includeFurniture: boolean;\r\n  airConditioning: boolean;\r\n  includeWaterHeater: boolean;\r\n  parking: boolean;\r\n  internet: boolean;\r\n  nearToMetro: boolean;\r\n  nearToMarket: boolean;\r\n  elevator: boolean;\r\n  trialPeriod: boolean;\r\n  goodForForeigners: boolean;\r\n  categoryId: string;\r\n  termsAndConditions: string;\r\n}\r\n\r\nexport const createProperty = async (data: Property) => {\r\n  console.log(\"create property data\", data);\r\n  const response = await post(\"/properties\", data);\r\n  console.log(\"create property response\", response);\r\n  return response;\r\n};\r\n\r\nexport const getProperties = async ({\r\n  page = 1,\r\n  limit = 12,\r\n  title = \"\",\r\n  city = \"\",\r\n  country = \"\",\r\n  neighborhood = \"\",\r\n  address = \"\",\r\n  type = \"\",\r\n  roomType = \"\",\r\n  genderRequired = \"\",\r\n  totalRooms = \"\",\r\n  availableRooms = \"\",\r\n  size = \"\",\r\n  floor = \"\",\r\n  bathrooms = \"\",\r\n  separatedBathroom = false,\r\n  residentsCount = \"\",\r\n  availablePersons = \"\",\r\n  minPrice = \"\",\r\n  maxPrice = \"\",\r\n  rentTime = \"\",\r\n  paymentTime = \"\",\r\n  priceIncludeWaterAndElectricity = false,\r\n  minRating = 0,\r\n  minTotalRatings = 0,\r\n  isVerified = false,\r\n  isAvailable = false,\r\n  categoryId = \"\",\r\n  allowSmoking = false,\r\n  includeFurniture = false,\r\n  airConditioning = false,\r\n  includeWaterHeater = false,\r\n  parking = false,\r\n  internet = false,\r\n  nearToMetro = false,\r\n  nearToMarket = false,\r\n  elevator = false,\r\n  trialPeriod = false,\r\n  goodForForeigners = false,\r\n  sortBy = \"createdAt\",\r\n  sortOrder = \"asc\",\r\n}: {\r\n  page?: number;\r\n  limit?: number;\r\n  title?: string;\r\n  city?: string;\r\n  country?: string;\r\n  neighborhood?: string;\r\n  address?: string;\r\n  latitude?: string;\r\n  longitude?: string;\r\n  type?: string;\r\n  roomType?: string;\r\n  genderRequired?: string;\r\n  totalRooms?: string;\r\n  availableRooms?: string;\r\n  size?: string;\r\n  floor?: string;\r\n  bathrooms?: string;\r\n  separatedBathroom?: boolean;\r\n  residentsCount?: string;\r\n  availablePersons?: string;\r\n  minPrice?: string;\r\n  maxPrice?: string;\r\n  rentTime?: string;\r\n  paymentTime?: string;\r\n  priceIncludeWaterAndElectricity?: boolean;\r\n  minRating?: number;\r\n  minTotalRatings?: number;\r\n  isVerified?: boolean;\r\n  isAvailable?: boolean;\r\n  categoryId?: string;\r\n  allowSmoking?: boolean;\r\n  includeFurniture?: boolean;\r\n  airConditioning?: boolean;\r\n  includeWaterHeater?: boolean;\r\n  parking?: boolean;\r\n  internet?: boolean;\r\n  nearToMetro?: boolean;\r\n  nearToMarket?: boolean;\r\n  elevator?: boolean;\r\n  trialPeriod?: boolean;\r\n  goodForForeigners?: boolean;\r\n  sortBy?: \"createdAt\" | \"updatedAt\" | \"price\" | \"rating\" | \"title\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n}) => {\r\n  const response = await get(\r\n    `/properties?page=${page}&limit=${limit}&${title ? `title=${title}&` : \"\"}${\r\n      city ? `city=${city}&` : \"\"\r\n    }${country ? `country=${country}&` : \"\"}${\r\n      neighborhood ? `neighborhood=${neighborhood}&` : \"\"\r\n    }${address ? `address=${address}&` : \"\"}${type ? `type=${type}&` : \"\"}${\r\n      roomType ? `roomType=${roomType}&` : \"\"\r\n    }${genderRequired ? `genderRequired=${genderRequired}&` : \"\"}${\r\n      totalRooms ? `totalRooms=${totalRooms}&` : \"\"\r\n    }${availableRooms ? `availableRooms=${availableRooms}&` : \"\"}${\r\n      size ? `size=${size}&` : \"\"\r\n    }${floor ? `floor=${floor}&` : \"\"}${\r\n      bathrooms ? `bathrooms=${bathrooms}&` : \"\"\r\n    }${separatedBathroom ? `separatedBathroom=${separatedBathroom}&` : \"\"}${\r\n      residentsCount ? `residentsCount=${residentsCount}&` : \"\"\r\n    }${availablePersons ? `availablePersons=${availablePersons}&` : \"\"}${\r\n      rentTime ? `rentTime=${rentTime}&` : \"\"\r\n    }${paymentTime ? `paymentTime=${paymentTime}&` : \"\"}${\r\n      priceIncludeWaterAndElectricity\r\n        ? `priceIncludeWaterAndElectricity=${priceIncludeWaterAndElectricity}&`\r\n        : \"\"\r\n    }${allowSmoking ? `allowSmoking=${allowSmoking}&` : \"\"}${\r\n      includeFurniture ? `includeFurniture=${includeFurniture}&` : \"\"\r\n    }${airConditioning ? `airConditioning=${airConditioning}&` : \"\"}${\r\n      includeWaterHeater ? `includeWaterHeater=${includeWaterHeater}&` : \"\"\r\n    }${parking ? `parking=${parking}&` : \"\"}${\r\n      internet ? `internet=${internet}&` : \"\"\r\n    }${nearToMetro ? `nearToMetro=${nearToMetro}&` : \"\"}${\r\n      nearToMarket ? `nearToMarket=${nearToMarket}&` : \"\"\r\n    }${elevator ? `elevator=${elevator}&` : \"\"}${\r\n      trialPeriod ? `trialPeriod=${trialPeriod}&` : \"\"\r\n    }${goodForForeigners ? `goodForForeigners=${goodForForeigners}&` : \"\"}${\r\n      categoryId ? `categoryId=${categoryId}&` : \"\"\r\n    }${minRating ? `minRating=${minRating}&` : \"\"}${\r\n      minTotalRatings ? `minTotalRatings=${minTotalRatings}&` : \"\"\r\n    }${isVerified ? `isVerified=${isVerified}&` : \"\"}${\r\n      minPrice ? `minPrice=${minPrice}&` : \"\"\r\n    }${maxPrice ? `maxPrice=${maxPrice}&` : \"\"}${\r\n      isAvailable ? `isAvailable=${isAvailable}&` : \"\"\r\n    }${sortBy ? `sortBy=${sortBy}&` : \"\"}${\r\n      sortOrder ? `sortOrder=${sortOrder}` : \"\"\r\n    }`\r\n  );\r\n\r\n  return response;\r\n};\r\n\r\nexport const getMyProperties = async () => {\r\n  const response = await get(`/properties/my-properties`);\r\n  return response;\r\n};\r\n\r\nexport const getProperty = async (slug: string) => {\r\n  const response = await get(`/properties/slug/${slug}`);\r\n  return response;\r\n};\r\n\r\nexport const getPropertiesByUserId = async (userId: string) => {\r\n  const response = await get(`/properties/user/${userId}`);\r\n  return response;\r\n};\r\n\r\nexport const getFavoriteProperties = async () => {\r\n  const response = await get(`/properties/favorites`);\r\n  return response;\r\n};\r\n\r\nexport const toggleFavorite = async (id: string) => {\r\n  const response = await patch(`/properties/${id}/toggle-favorite`);\r\n  console.log(\"toggle favorite response\", response);\r\n  return response;\r\n};\r\n\r\nexport const updateProperty = async (data: Property) => {\r\n  const response = await patch(\"/properties\", data);\r\n  return response;\r\n};\r\n\r\nexport const deleteProperty = async (id: string) => {\r\n  const response = await del(`/properties/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAwMa,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/hooks/useFavoriteProperties.ts"], "sourcesContent": ["import { useQuery } from \"@tanstack/react-query\";\r\nimport { getFavoriteProperties } from \"@/actions/properties\";\r\n\r\nexport function useFavoriteProperties() {\r\n  const {\r\n    data: favoritesResponse,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n  } = useQuery({\r\n    queryKey: [\"favorites\", \"properties\"],\r\n    queryFn: getFavoriteProperties,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n  });\r\n\r\n  // Extract the actual favorites data from the nested response structure\r\n  const favorites = favoritesResponse?.data?.data || [];\r\n\r\n  // Function to check if a property is in favorites\r\n  const isPropertyFavorite = (propertyId: string): boolean => {\r\n    return favorites.some((favorite: any) => favorite.id === propertyId);\r\n  };\r\n\r\n  // Function to get favorite property by ID\r\n  const getFavoriteProperty = (propertyId: string) => {\r\n    return favorites.find((favorite: any) => favorite.id === propertyId);\r\n  };\r\n\r\n  return {\r\n    favorites,\r\n    favoritesCount: favorites.length,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n    isPropertyFavorite,\r\n    getFavoriteProperty,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAEO,SAAS;;IACd,MAAM,EACJ,MAAM,iBAAiB,EACvB,SAAS,EACT,OAAO,EACP,KAAK,EACN,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACX,UAAU;YAAC;YAAa;SAAa;QACrC,SAAS,yJAAA,CAAA,wBAAqB;QAC9B,WAAW,IAAI,KAAK;IACtB;IAEA,uEAAuE;IACvE,MAAM,YAAY,mBAAmB,MAAM,QAAQ,EAAE;IAErD,kDAAkD;IAClD,MAAM,qBAAqB,CAAC;QAC1B,OAAO,UAAU,IAAI,CAAC,CAAC,WAAkB,SAAS,EAAE,KAAK;IAC3D;IAEA,0CAA0C;IAC1C,MAAM,sBAAsB,CAAC;QAC3B,OAAO,UAAU,IAAI,CAAC,CAAC,WAAkB,SAAS,EAAE,KAAK;IAC3D;IAEA,OAAO;QACL;QACA,gBAAgB,UAAU,MAAM;QAChC;QACA;QACA;QACA;QACA;IACF;AACF;GAlCgB;;QAMV,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 1390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/persons.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { del, get, patch, post } from \"@/services/api\";\r\n\r\ninterface Person {\r\n  title: string;\r\n  images: string[];\r\n  city: string;\r\n  country: string;\r\n  neighborhood: string;\r\n  latitude: string;\r\n  longitude: string;\r\n  address: string;\r\n  description: string;\r\n  type: string;\r\n  roomType: string;\r\n  genderRequired: string;\r\n  totalRooms: string;\r\n  availableRooms: string;\r\n  price: string;\r\n  size: string;\r\n  floor: string;\r\n  bathrooms: string;\r\n  separatedBathroom: boolean;\r\n  residentsCount: string;\r\n  availablePersons: string;\r\n  rentTime: string;\r\n  paymentTime: string;\r\n  priceIncludeWaterAndElectricity: boolean;\r\n  allowSmoking: boolean;\r\n  includeFurniture: boolean;\r\n  airConditioning: boolean;\r\n  includeWaterHeater: boolean;\r\n  parking: boolean;\r\n  internet: boolean;\r\n  nearToMetro: boolean;\r\n  nearToMarket: boolean;\r\n  elevator: boolean;\r\n  trialPeriod: boolean;\r\n  goodForForeigners: boolean;\r\n  categoryId: string;\r\n  termsAndConditions: string;\r\n}\r\n\r\nexport const createPerson = async (data: Person) => {\r\n  const response = await post(\"/persons\", data);\r\n  return response;\r\n};\r\n\r\nexport const getPersons = async ({\r\n  page = 1,\r\n  limit = 12,\r\n  title = \"\",\r\n  city = \"\",\r\n  country = \"\",\r\n  neighborhood = \"\",\r\n  address = \"\",\r\n  type = \"\",\r\n  roomType = \"\",\r\n  genderRequired = \"\",\r\n  totalRooms = \"\",\r\n  availableRooms = \"\",\r\n  size = \"\",\r\n  floor = \"\",\r\n  bathrooms = \"\",\r\n  separatedBathroom = false,\r\n  residentsCount = \"\",\r\n  availablePersons = \"\",\r\n  minPrice = \"\",\r\n  maxPrice = \"\",\r\n  rentTime = \"\",\r\n  paymentTime = \"\",\r\n  priceIncludeWaterAndElectricity = false,\r\n  minRating = 0,\r\n  minTotalRatings = 0,\r\n  isVerified = false,\r\n  isAvailable = false,\r\n  categoryId = \"\",\r\n  allowSmoking = false,\r\n  includeFurniture = false,\r\n  airConditioning = false,\r\n  includeWaterHeater = false,\r\n  parking = false,\r\n  internet = false,\r\n  nearToMetro = false,\r\n  nearToMarket = false,\r\n  elevator = false,\r\n  trialPeriod = false,\r\n  goodForForeigners = false,\r\n  sortBy = \"createdAt\",\r\n  sortOrder = \"asc\",\r\n}: {\r\n  page?: number;\r\n  limit?: number;\r\n  title?: string;\r\n  city?: string;\r\n  country?: string;\r\n  neighborhood?: string;\r\n  address?: string;\r\n  latitude?: string;\r\n  longitude?: string;\r\n  type?: string;\r\n  roomType?: string;\r\n  genderRequired?: string;\r\n  totalRooms?: string;\r\n  availableRooms?: string;\r\n  size?: string;\r\n  floor?: string;\r\n  bathrooms?: string;\r\n  separatedBathroom?: boolean;\r\n  residentsCount?: string;\r\n  availablePersons?: string;\r\n  minPrice?: string;\r\n  maxPrice?: string;\r\n  rentTime?: string;\r\n  paymentTime?: string;\r\n  priceIncludeWaterAndElectricity?: boolean;\r\n  minRating?: number;\r\n  minTotalRatings?: number;\r\n  isVerified?: boolean;\r\n  isAvailable?: boolean;\r\n  categoryId?: string;\r\n  allowSmoking?: boolean;\r\n  includeFurniture?: boolean;\r\n  airConditioning?: boolean;\r\n  includeWaterHeater?: boolean;\r\n  parking?: boolean;\r\n  internet?: boolean;\r\n  nearToMetro?: boolean;\r\n  nearToMarket?: boolean;\r\n  elevator?: boolean;\r\n  trialPeriod?: boolean;\r\n  goodForForeigners?: boolean;\r\n  sortBy?: \"createdAt\" | \"updatedAt\" | \"price\" | \"rating\" | \"title\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n}) => {\r\n  const response = await get(\r\n    `/persons?page=${page}&limit=${limit}&${title ? `title=${title}&` : \"\"}${\r\n      city ? `city=${city}&` : \"\"\r\n    }${country ? `country=${country}&` : \"\"}${\r\n      neighborhood ? `neighborhood=${neighborhood}&` : \"\"\r\n    }${address ? `address=${address}&` : \"\"}${type ? `type=${type}&` : \"\"}${\r\n      roomType ? `roomType=${roomType}&` : \"\"\r\n    }${genderRequired ? `genderRequired=${genderRequired}&` : \"\"}${\r\n      totalRooms ? `totalRooms=${totalRooms}&` : \"\"\r\n    }${availableRooms ? `availableRooms=${availableRooms}&` : \"\"}${\r\n      size ? `size=${size}&` : \"\"\r\n    }${floor ? `floor=${floor}&` : \"\"}${\r\n      bathrooms ? `bathrooms=${bathrooms}&` : \"\"\r\n    }${separatedBathroom ? `separatedBathroom=${separatedBathroom}&` : \"\"}${\r\n      residentsCount ? `residentsCount=${residentsCount}&` : \"\"\r\n    }${availablePersons ? `availablePersons=${availablePersons}&` : \"\"}${\r\n      rentTime ? `rentTime=${rentTime}&` : \"\"\r\n    }${paymentTime ? `paymentTime=${paymentTime}&` : \"\"}${\r\n      priceIncludeWaterAndElectricity\r\n        ? `priceIncludeWaterAndElectricity=${priceIncludeWaterAndElectricity}&`\r\n        : \"\"\r\n    }${allowSmoking ? `allowSmoking=${allowSmoking}&` : \"\"}${\r\n      includeFurniture ? `includeFurniture=${includeFurniture}&` : \"\"\r\n    }${airConditioning ? `airConditioning=${airConditioning}&` : \"\"}${\r\n      includeWaterHeater ? `includeWaterHeater=${includeWaterHeater}&` : \"\"\r\n    }${parking ? `parking=${parking}&` : \"\"}${\r\n      internet ? `internet=${internet}&` : \"\"\r\n    }${nearToMetro ? `nearToMetro=${nearToMetro}&` : \"\"}${\r\n      nearToMarket ? `nearToMarket=${nearToMarket}&` : \"\"\r\n    }${elevator ? `elevator=${elevator}&` : \"\"}${\r\n      trialPeriod ? `trialPeriod=${trialPeriod}&` : \"\"\r\n    }${goodForForeigners ? `goodForForeigners=${goodForForeigners}&` : \"\"}${\r\n      categoryId ? `categoryId=${categoryId}&` : \"\"\r\n    }${minRating ? `minRating=${minRating}&` : \"\"}${\r\n      minTotalRatings ? `minTotalRatings=${minTotalRatings}&` : \"\"\r\n    }${isVerified ? `isVerified=${isVerified}&` : \"\"}${\r\n      isAvailable ? `isAvailable=${isAvailable}&` : \"\"\r\n    }${minPrice ? `minPrice=${minPrice}&` : \"\"}${\r\n      maxPrice ? `maxPrice=${maxPrice}&` : \"\"\r\n    }${sortBy ? `sortBy=${sortBy}&` : \"\"}${\r\n      sortOrder ? `sortOrder=${sortOrder}` : \"\"\r\n    }`\r\n  );\r\n  return response;\r\n};\r\n\r\nexport const getMyPersons = async () => {\r\n  const response = await get(`/persons/my-persons`);\r\n  return response;\r\n};\r\n\r\nexport const getPerson = async (slug: string) => {\r\n  const response = await get(`/persons/slug/${slug}`);\r\n  return response;\r\n};\r\n\r\nexport const getPersonsByUserId = async (userId: string) => {\r\n  const response = await get(`/persons/user/${userId}`);\r\n  return response;\r\n};\r\n\r\nexport const getFavoritePersons = async () => {\r\n  const response = await get(`/persons/favorites`);\r\n  return response;\r\n};\r\n\r\nexport const toggleFavorite = async (id: string) => {\r\n  const response = await patch(`/persons/${id}/toggle-favorite`);\r\n  return response;\r\n};\r\n\r\nexport const updatePerson = async (data: Person) => {\r\n  const response = await patch(\"/persons\", data);\r\n  return response;\r\n};\r\n\r\nexport const deletePerson = async (id: string) => {\r\n  const response = await del(`/persons/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAqMa,qBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/hooks/useFavoritePersons.ts"], "sourcesContent": ["import { useQuery } from \"@tanstack/react-query\";\r\nimport { getFavoritePersons } from \"@/actions/persons\";\r\n\r\nexport function useFavoritePersons() {\r\n  const {\r\n    data: favoritesResponse,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n  } = useQuery({\r\n    queryKey: [\"favorites\", \"persons\"],\r\n    queryFn: getFavoritePersons,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n  });\r\n\r\n  // Extract the actual favorites data from the nested response structure\r\n  const favorites = favoritesResponse?.data?.data || [];\r\n\r\n  // Function to check if a person is in favorites\r\n  const isPersonFavorite = (personId: string): boolean => {\r\n    return favorites.some((favorite: any) => favorite.id === personId);\r\n  };\r\n\r\n  // Function to get favorite person by ID\r\n  const getFavoritePerson = (personId: string) => {\r\n    return favorites.find((favorite: any) => favorite.id === personId);\r\n  };\r\n\r\n  return {\r\n    favorites,\r\n    favoritesCount: favorites.length,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n    isPersonFavorite,\r\n    getFavorite<PERSON>erson,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAEO,SAAS;;IACd,MAAM,EACJ,MAAM,iBAAiB,EACvB,SAAS,EACT,OAAO,EACP,KAAK,EACN,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACX,UAAU;YAAC;YAAa;SAAU;QAClC,SAAS,yJAAA,CAAA,qBAAkB;QAC3B,WAAW,IAAI,KAAK;IACtB;IAEA,uEAAuE;IACvE,MAAM,YAAY,mBAAmB,MAAM,QAAQ,EAAE;IAErD,gDAAgD;IAChD,MAAM,mBAAmB,CAAC;QACxB,OAAO,UAAU,IAAI,CAAC,CAAC,WAAkB,SAAS,EAAE,KAAK;IAC3D;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,CAAC;QACzB,OAAO,UAAU,IAAI,CAAC,CAAC,WAAkB,SAAS,EAAE,KAAK;IAC3D;IAEA,OAAO;QACL;QACA,gBAAgB,UAAU,MAAM;QAChC;QACA;QACA;QACA;QACA;IACF;AACF;GAlCgB;;QAMV,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 1458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/app/%5Blocale%5D/%28root%29/my-ads/components/MyAdsContent.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { useQ<PERSON>y, useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useTranslations } from 'next-intl'\r\nimport { Loader2, AlertCircle, UserX, Home } from 'lucide-react'\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport { TypeToggle } from '@/components/homepage/type-toggle'\r\nimport { PropertyCard } from '@/components/homepage/property-card'\r\nimport { getMyProperties, toggleFavorite as togglePropertyFavorite } from '@/actions/properties'\r\nimport { getMyPersons, toggleFavorite as togglePersonFavorite } from '@/actions/persons'\r\nimport { useFavoriteProperties } from '@/hooks/useFavoriteProperties'\r\nimport { useFavoritePersons } from '@/hooks/useFavoritePersons'\r\nimport { useUserStore } from '@/store/useUserStore'\r\nimport { cn } from '@/lib/utils'\r\n\r\ntype ViewType = 'properties' | 'persons'\r\ntype ViewMode = 'grid' | 'list'\r\n\r\n// Property interface matching the PropertyCard component\r\ninterface Property {\r\n  id: string\r\n  title: string\r\n  slug: string\r\n  images: string[]\r\n  city: string\r\n  country: string\r\n  neighborhood: string\r\n  address: string\r\n  description: string\r\n  type: string\r\n  roomType: string\r\n  genderRequired: string\r\n  totalRooms: string\r\n  availableRooms: string\r\n  price: string\r\n  size: string\r\n  floor: string\r\n  bathrooms: string\r\n  separatedBathroom: boolean\r\n  residentsCount: string\r\n  availablePersons: string\r\n  rentTime: string\r\n  paymentTime: string\r\n  priceIncludeWaterAndElectricity: boolean\r\n  allowSmoking: boolean\r\n  includeFurniture: boolean\r\n  airConditioning: boolean\r\n  includeWaterHeater: boolean\r\n  parking: boolean\r\n  internet: boolean\r\n  nearToMetro: boolean\r\n  nearToMarket: boolean\r\n  elevator: boolean\r\n  trialPeriod: boolean\r\n  goodForForeigners: boolean\r\n  categoryId: string\r\n  isVerified?: boolean\r\n  isAvailable?: boolean\r\n  isFavorite?: boolean\r\n  rating?: number\r\n  totalRatings?: number\r\n  user?: {\r\n    name: string\r\n    avatar?: string\r\n  }\r\n  createdAt: string\r\n  updatedAt: string\r\n}\r\n\r\n// Person interface with similar structure to Property\r\ninterface Person {\r\n  id: string\r\n  title: string\r\n  slug: string\r\n  images: string[]\r\n  city: string\r\n  country: string\r\n  neighborhood: string\r\n  address: string\r\n  description: string\r\n  type: string\r\n  roomType: string\r\n  genderRequired: string\r\n  totalRooms: string\r\n  availableRooms: string\r\n  price: string\r\n  size: string\r\n  floor: string\r\n  bathrooms: string\r\n  separatedBathroom: boolean\r\n  residentsCount: string\r\n  availablePersons: string\r\n  rentTime: string\r\n  paymentTime: string\r\n  priceIncludeWaterAndElectricity: boolean\r\n  allowSmoking: boolean\r\n  includeFurniture: boolean\r\n  airConditioning: boolean\r\n  includeWaterHeater: boolean\r\n  parking: boolean\r\n  internet: boolean\r\n  nearToMetro: boolean\r\n  nearToMarket: boolean\r\n  elevator: boolean\r\n  trialPeriod: boolean\r\n  goodForForeigners: boolean\r\n  categoryId: string\r\n  isVerified?: boolean\r\n  isAvailable?: boolean\r\n  isFavorite?: boolean\r\n  rating?: number\r\n  totalRatings?: number\r\n  user?: {\r\n    name: string\r\n    avatar?: string\r\n  }\r\n  createdAt: string\r\n  updatedAt: string\r\n}\r\n\r\n// Union type for items that can be either Property or Person\r\ntype PropertyOrPerson = Property | Person\r\n\r\ninterface HomepageProps {\r\n  className?: string\r\n}\r\n\r\nexport function MyAdsContent({ className }: HomepageProps) {\r\n  const t = useTranslations('homepage')\r\n  const queryClient = useQueryClient()\r\n\r\n  // User authentication\r\n  const { isAuthenticated } = useUserStore()\r\n\r\n  // State management\r\n  const [viewType, setViewType] = useState<ViewType>('properties')\r\n  const [viewMode] = useState<ViewMode>('grid')\r\n\r\n  // Favorite hooks\r\n  const { isPropertyFavorite } = useFavoriteProperties()\r\n  const { isPersonFavorite } = useFavoritePersons()\r\n\r\n  // Properties query - fetch only user's properties\r\n  const propertiesQuery = useQuery({\r\n    queryKey: ['my-properties'],\r\n    queryFn: () => getMyProperties(),\r\n    enabled: viewType === 'properties' && isAuthenticated,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n  })\r\n\r\n  // Persons query - fetch only user's persons\r\n  const personsQuery = useQuery({\r\n    queryKey: ['my-persons'],\r\n    queryFn: () => getMyPersons(),\r\n    enabled: viewType === 'persons' && isAuthenticated,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n  })\r\n\r\n  // Mutations for favorite toggle\r\n  const togglePropertyFavoriteMutation = useMutation({\r\n    mutationFn: (id: string) => togglePropertyFavorite(id),\r\n    onSuccess: () => {\r\n      // Invalidate and refetch user's properties data and favorites\r\n      queryClient.invalidateQueries({ queryKey: ['my-properties'] })\r\n      queryClient.invalidateQueries({ queryKey: ['favorites', 'properties'] })\r\n    },\r\n    onError: (error) => {\r\n      console.error('Failed to toggle property favorite:', error)\r\n    }\r\n  })\r\n\r\n  const togglePersonFavoriteMutation = useMutation({\r\n    mutationFn: (id: string) => togglePersonFavorite(id),\r\n    onSuccess: () => {\r\n      // Invalidate and refetch user's persons data and favorites\r\n      queryClient.invalidateQueries({ queryKey: ['my-persons'] })\r\n      queryClient.invalidateQueries({ queryKey: ['favorites', 'persons'] })\r\n    },\r\n    onError: (error) => {\r\n      console.error('Failed to toggle person favorite:', error)\r\n    }\r\n  })\r\n\r\n  // Show authentication required message if user is not authenticated\r\n  if (!isAuthenticated) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-[400px]\">\r\n        <div className=\"text-center space-y-4\">\r\n          <AlertCircle className=\"h-8 w-8 mx-auto text-destructive\" />\r\n          <div>\r\n            <h3 className=\"font-semibold mb-2\">Authentication Required</h3>\r\n            <p className=\"text-muted-foreground mb-4\">\r\n              Please log in to view your ads.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // Get current query based on view type\r\n  const currentQuery = viewType === 'properties' ? propertiesQuery : personsQuery\r\n\r\n  // Handle different API response structures for user-specific endpoints\r\n  // API service wraps response in { data: actualResponse, status: number }\r\n  const apiResponse = currentQuery.data?.data\r\n\r\n  // For user-specific endpoints, the structure might be different\r\n  const currentData = viewType === 'properties'\r\n    ? apiResponse?.data || apiResponse || []\r\n    : apiResponse?.data || apiResponse || []\r\n\r\n  // Handle favorite toggle\r\n  const handleFavoriteToggle = (id: string) => {\r\n    if (viewType === 'properties') {\r\n      togglePropertyFavoriteMutation.mutate(id)\r\n    } else {\r\n      togglePersonFavoriteMutation.mutate(id)\r\n    }\r\n  }\r\n\r\n  // Loading state for initial load\r\n  if (currentQuery.isLoading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-[400px]\">\r\n        <div className=\"text-center space-y-4\">\r\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto text-primary\" />\r\n          <p className=\"text-muted-foreground\">\r\n            {viewType === 'properties' ? t('loading.properties') : t('loading.persons')}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // Error state\r\n  if (currentQuery.isError) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-[400px]\">\r\n        <div className=\"text-center space-y-4\">\r\n          <AlertCircle className=\"h-8 w-8 mx-auto text-destructive\" />\r\n          <div>\r\n            <h3 className=\"font-semibold mb-2\">Something went wrong</h3>\r\n            <p className=\"text-muted-foreground mb-4\">\r\n              {viewType === 'properties' ? t('errors.loadingProperties') : t('errors.loadingPersons')}\r\n            </p>\r\n            <Button onClick={() => currentQuery.refetch()}>\r\n              Try Again\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className={cn(\"w-full space-y-6\", className)}>\r\n      {/* Filters Section */}\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex flex-col sm:flex-row items-start sm:items-center gap-4 justify-between\">\r\n          <h1 className=\"text-3xl font-bold text-gray-900\">My Ads</h1>\r\n          <div className=\"flex flex-col sm:flex-row items-start sm:items-center gap-4 justify-between\">\r\n            <TypeToggle value={viewType} onChange={setViewType} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Results Section */}\r\n      <div className=\"space-y-6\">\r\n        {/* Results Count */}\r\n        {/* {currentPagination && (\r\n                    <div className=\"text-sm text-muted-foreground\">\r\n                        {t('navigation.showingResults', {\r\n                            current: currentData.length,\r\n                            total: currentPagination.total\r\n                        })}\r\n                    </div>\r\n                )} */}\r\n\r\n        {/* Grid */}\r\n        {currentData.length === 0 ? (\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"max-w-md mx-auto space-y-4\">\r\n              <div className=\"flex justify-center\">\r\n                {viewType === 'properties' ? (\r\n                  <Home className=\"h-16 w-16 text-muted-foreground animate-bounce\" />\r\n                ) : (\r\n                  <UserX className=\"h-16 w-16 text-muted-foreground animate-bounce\" />\r\n                )}\r\n              </div>\r\n              <h3 className=\"text-lg font-semibold\">\r\n                {viewType === 'properties' ? t('empty.properties') : t('empty.persons')}\r\n              </h3>\r\n              <p className=\"text-muted-foreground\">\r\n                {t('empty.tryAdjusting')}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className={cn(\r\n            \"grid gap-6\",\r\n            viewMode === 'grid'\r\n              ? \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3\"\r\n              : \"grid-cols-1\"\r\n          )}>\r\n            {Array.isArray(currentData) && currentData.map((item: PropertyOrPerson) => (\r\n              viewType === 'properties' ? (\r\n                <PropertyCard\r\n                  key={item.id}\r\n                  property={item}\r\n                  onFavoriteToggle={handleFavoriteToggle}\r\n                  viewType={viewType}\r\n                  isFavoriteLoading={togglePropertyFavoriteMutation.isPending}\r\n                  isFavorite={isPropertyFavorite(item.id)}\r\n                />\r\n              ) : (\r\n                <PropertyCard\r\n                  key={item.id}\r\n                  property={item}\r\n                  onFavoriteToggle={handleFavoriteToggle}\r\n                  viewType={viewType}\r\n                  isFavoriteLoading={togglePersonFavoriteMutation.isPending}\r\n                  isFavorite={isPersonFavorite(item.id)}\r\n                />\r\n              )\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;;AAgIO,SAAS,aAAa,EAAE,SAAS,EAAiB;;IACvD,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,sBAAsB;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC,mBAAmB;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IAEtC,iBAAiB;IACjB,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD;IACnD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;IAE9C,kDAAkD;IAClD,MAAM,kBAAkB,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,UAAU;YAAC;SAAgB;QAC3B,OAAO;sDAAE,IAAM,CAAA,GAAA,yJAAA,CAAA,kBAAe,AAAD;;QAC7B,SAAS,aAAa,gBAAgB;QACtC,WAAW,IAAI,KAAK;IACtB;IAEA,4CAA4C;IAC5C,MAAM,eAAe,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC5B,UAAU;YAAC;SAAa;QACxB,OAAO;mDAAE,IAAM,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;;QAC1B,SAAS,aAAa,aAAa;QACnC,WAAW,IAAI,KAAK;IACtB;IAEA,gCAAgC;IAChC,MAAM,iCAAiC,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjD,UAAU;wEAAE,CAAC,KAAe,CAAA,GAAA,yJAAA,CAAA,iBAAsB,AAAD,EAAE;;QACnD,SAAS;wEAAE;gBACT,8DAA8D;gBAC9D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAgB;gBAAC;gBAC5D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAa;qBAAa;gBAAC;YACxE;;QACA,OAAO;wEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,uCAAuC;YACvD;;IACF;IAEA,MAAM,+BAA+B,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC/C,UAAU;sEAAE,CAAC,KAAe,CAAA,GAAA,yJAAA,CAAA,iBAAoB,AAAD,EAAE;;QACjD,SAAS;sEAAE;gBACT,2DAA2D;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAa;gBAAC;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAa;qBAAU;gBAAC;YACrE;;QACA,OAAO;sEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,qCAAqC;YACrD;;IACF;IAEA,oEAAoE;IACpE,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;IAOpD;IAEA,uCAAuC;IACvC,MAAM,eAAe,aAAa,eAAe,kBAAkB;IAEnE,uEAAuE;IACvE,yEAAyE;IACzE,MAAM,cAAc,aAAa,IAAI,EAAE;IAEvC,gEAAgE;IAChE,MAAM,cAAc,aAAa,eAC7B,aAAa,QAAQ,eAAe,EAAE,GACtC,aAAa,QAAQ,eAAe,EAAE;IAE1C,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,IAAI,aAAa,cAAc;YAC7B,+BAA+B,MAAM,CAAC;QACxC,OAAO;YACL,6BAA6B,MAAM,CAAC;QACtC;IACF;IAEA,iCAAiC;IACjC,IAAI,aAAa,SAAS,EAAE;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCACV,aAAa,eAAe,EAAE,wBAAwB,EAAE;;;;;;;;;;;;;;;;;IAKnE;IAEA,cAAc;IACd,IAAI,aAAa,OAAO,EAAE;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAE,WAAU;0CACV,aAAa,eAAe,EAAE,8BAA8B,EAAE;;;;;;0CAEjE,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,aAAa,OAAO;0CAAI;;;;;;;;;;;;;;;;;;;;;;;IAOzD;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;;0BAErC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mJAAA,CAAA,aAAU;gCAAC,OAAO;gCAAU,UAAU;;;;;;;;;;;;;;;;;;;;;;0BAM7C,6LAAC;gBAAI,WAAU;0BAYZ,YAAY,MAAM,KAAK,kBACtB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,aAAa,6BACZ,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,6LAAC,2MAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAGrB,6LAAC;gCAAG,WAAU;0CACX,aAAa,eAAe,EAAE,sBAAsB,EAAE;;;;;;0CAEzD,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;;;;;yCAKT,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,cACA,aAAa,SACT,8CACA;8BAEH,MAAM,OAAO,CAAC,gBAAgB,YAAY,GAAG,CAAC,CAAC,OAC9C,aAAa,6BACX,6LAAC,qJAAA,CAAA,eAAY;4BAEX,UAAU;4BACV,kBAAkB;4BAClB,UAAU;4BACV,mBAAmB,+BAA+B,SAAS;4BAC3D,YAAY,mBAAmB,KAAK,EAAE;2BALjC,KAAK,EAAE;;;;iDAQd,6LAAC,qJAAA,CAAA,eAAY;4BAEX,UAAU;4BACV,kBAAkB;4BAClB,UAAU;4BACV,mBAAmB,6BAA6B,SAAS;4BACzD,YAAY,iBAAiB,KAAK,EAAE;2BAL/B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAc9B;GA5MgB;;QACJ,yMAAA,CAAA,kBAAe;QACL,yLAAA,CAAA,iBAAc;QAGN,+HAAA,CAAA,eAAY;QAOT,wIAAA,CAAA,wBAAqB;QACvB,qIAAA,CAAA,qBAAkB;QAGvB,8KAAA,CAAA,WAAQ;QAQX,8KAAA,CAAA,WAAQ;QAQU,iLAAA,CAAA,cAAW;QAYb,iLAAA,CAAA,cAAW;;;KA5ClC", "debugId": null}}]}
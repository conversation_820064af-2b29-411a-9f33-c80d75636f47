import { CreatePropertyDto } from './dto/create-property.dto';
import { UpdatePropertyDto } from './dto/update-property.dto';
import { QueryPropertiesDto } from './dto/query-properties.dto';
export declare const createPropertyExample: CreatePropertyDto;
export declare const minimalPropertyExample: CreatePropertyDto;
export declare const sharedRoomExample: CreatePropertyDto;
export declare const updatePropertyExample: UpdatePropertyDto;
export declare const queryExamples: {
    getAllProperties: QueryPropertiesDto;
    searchProperties: QueryPropertiesDto;
    filterByTitle: QueryPropertiesDto;
    filterBySlug: QueryPropertiesDto;
    filterByLocation: QueryPropertiesDto;
    filterByAddress: QueryPropertiesDto;
    filterByCoordinates: QueryPropertiesDto;
    filterByType: QueryPropertiesDto;
    filterByGender: QueryPropertiesDto;
    filterByRoomSpecs: QueryPropertiesDto;
    filterBySpaceSpecs: QueryPropertiesDto;
    filterByPrice: QueryPropertiesDto;
    filterByTerms: QueryPropertiesDto;
    filterByCategory: QueryPropertiesDto;
    filterByOwner: QueryPropertiesDto;
    filterByRating: QueryPropertiesDto;
    filterByDateRange: QueryPropertiesDto;
    filterByRecentUpdates: QueryPropertiesDto;
    filterByStatus: QueryPropertiesDto;
    filterByRentalPrefs: QueryPropertiesDto;
    filterByAmenities: QueryPropertiesDto;
    filterByLocationAmenities: QueryPropertiesDto;
    complexFilter: QueryPropertiesDto;
    premiumFilter: QueryPropertiesDto;
    budgetFilter: QueryPropertiesDto;
    studentHousingFilter: QueryPropertiesDto;
    familyFilter: QueryPropertiesDto;
    sharedFilter: QueryPropertiesDto;
    executiveFilter: QueryPropertiesDto;
    roomCompletionFilter: QueryPropertiesDto;
    highRatedFilter: QueryPropertiesDto;
};
export declare const validationExamples: {
    validPropertyTypes: ("house" | "room")[];
    validRoomTypes: ("mixed" | "single")[];
    validRentTimes: ("daily" | "weekly" | "monthly" | "quarterly" | "semiannual" | "annually")[];
    validPaymentTimes: ("daily" | "weekly" | "monthly" | "quarterly" | "semiannual" | "annually")[];
    validGenderRequirements: string[];
    validAmenities: string[];
    invalidExamples: ({
        title: string;
        description?: undefined;
        categoryId?: undefined;
        termsAndConditions?: undefined;
    } | {
        description: string;
        title?: undefined;
        categoryId?: undefined;
        termsAndConditions?: undefined;
    } | {
        categoryId: string;
        title?: undefined;
        description?: undefined;
        termsAndConditions?: undefined;
    } | {
        termsAndConditions: string;
        title?: undefined;
        description?: undefined;
        categoryId?: undefined;
    })[];
};
export declare const apiEndpointExamples: {
    create: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: CreatePropertyDto;
    };
    getAll: {
        method: string;
        url: string;
    };
    search: {
        method: string;
        url: string;
    };
    getByCategory: {
        method: string;
        url: string;
    };
    getUserProperties: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getFavorites: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getById: {
        method: string;
        url: string;
    };
    getBySlug: {
        method: string;
        url: string;
    };
    update: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: UpdatePropertyDto;
    };
    toggleFavorite: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    delete: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
};
export declare const responseExamples: {
    propertyResponse: {
        id: string;
        title: string;
        slug: string;
        images: string[];
        city: string;
        country: string;
        address: string;
        description: string;
        latitude: string;
        longitude: string;
        type: string;
        roomType: string;
        genderRequired: string;
        totalRooms: string;
        availableRooms: string;
        price: string;
        size: string;
        floor: string;
        bathrooms: string;
        separatedBathroom: boolean;
        residentsCount: string;
        availablePersons: string;
        rentTime: string;
        paymentTime: string;
        priceIncludeWaterAndElectricity: boolean;
        allowSmoking: boolean;
        includeFurniture: boolean;
        airConditioning: boolean;
        parking: boolean;
        internet: boolean;
        nearToMetro: boolean;
        goodForForeigners: boolean;
        rating: number;
        totalRatings: number;
        isVerified: boolean;
        isAvailable: boolean;
        createdAt: string;
        updatedAt: string;
        user: {
            id: string;
            name: string;
            email: string;
            phone: string;
        };
        category: {
            id: string;
            name: string;
            icon: string;
        };
        offers: {
            id: string;
            message: string;
            price: string;
            status: string;
            user: {
                name: string;
                email: string;
            };
        }[];
        bookings: {
            id: string;
            startDate: string;
            endDate: string;
            status: string;
        }[];
    };
    paginatedResponse: {
        data: never[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
    statsResponse: {
        totalProperties: number;
        availableProperties: number;
        bookedProperties: number;
        propertiesByType: {
            APARTMENT: number;
            HOUSE: number;
            STUDIO: number;
            SHARED_ROOM: number;
        };
        propertiesByCity: {
            Cairo: number;
            Alexandria: number;
            Giza: number;
            Others: number;
        };
        avgPrice: number;
        avgSize: number;
        propertiesWithAmenities: {
            airConditioning: number;
            parking: number;
            internet: number;
            includeFurniture: number;
        };
        newPropertiesThisMonth: number;
    };
};

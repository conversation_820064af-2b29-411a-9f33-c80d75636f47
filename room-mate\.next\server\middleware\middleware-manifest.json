{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_e4d10b84._.js", "server/edge/chunks/[root-of-the-server]__6e947228._.js", "server/edge/chunks/edge-wrapper_248d3c1f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|trpc|_next|_vercel|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|trpc|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "R/oltUjaDIKpnKOdmz/MZdAlO+ID1g2d+s/OfLClTjI=", "__NEXT_PREVIEW_MODE_ID": "3cec20df6aa376aa57f48fc93a1adcf0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1ceaca60ac91a5b3806e09086207638d8bdd4087fa7fa66bea7f24cef3844617", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "800229dd5256cb9979ab0d4750525e4bbb7bb22085dfe32fa226563a5cbebea9"}}}, "instrumentation": null, "functions": {}}
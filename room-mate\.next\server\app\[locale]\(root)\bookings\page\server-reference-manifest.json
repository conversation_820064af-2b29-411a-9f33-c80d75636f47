{"node": {"7f7ed2832f654977865887574304abff898a3508c9": {"workers": {"app/[locale]/(root)/bookings/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/bookings/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/bookings/page": "action-browser"}}, "7fc81d422d946583d67e965e01874ba827ddfb076a": {"workers": {"app/[locale]/(root)/bookings/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/bookings/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/bookings/page": "action-browser"}}, "7ff5228fd53ce23c0ab89ce73ea7504be3fdad58ed": {"workers": {"app/[locale]/(root)/bookings/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/bookings/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/bookings/page": "action-browser"}}, "7f66a103c6ddae774301f8b2622c6ae732936859ae": {"workers": {"app/[locale]/(root)/bookings/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/bookings/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/bookings/page": "action-browser"}}, "7fce98afd99c671ec24efe38503d6078d69fdd6123": {"workers": {"app/[locale]/(root)/bookings/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/bookings/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/bookings/page": "action-browser"}}}, "edge": {}}
'use client'

import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { Loader2, AlertCircle, UserX, Home } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TypeToggle } from '@/components/homepage/type-toggle'
import { PropertyCard } from '@/components/homepage/property-card'
import { getProperties, toggleFavorite as togglePropertyFavorite } from '@/actions/properties'
import { getPersons, toggleFavorite as togglePersonFavorite } from '@/actions/persons'
import { useFavoriteProperties } from '@/hooks/useFavoriteProperties'
import { useFavoritePersons } from '@/hooks/useFavoritePersons'
import { cn } from '@/lib/utils'

type ViewType = 'properties' | 'persons'
type SortOption = 'newest' | 'oldest' | 'price-high' | 'price-low' | 'popular'
type ViewMode = 'grid' | 'list'

// Property interface matching the PropertyCard component
interface Property {
  id: string
  title: string
  slug: string
  images: string[]
  city: string
  country: string
  neighborhood: string
  address: string
  description: string
  type: string
  roomType: string
  genderRequired: string
  totalRooms: string
  availableRooms: string
  price: string
  size: string
  floor: string
  bathrooms: string
  separatedBathroom: boolean
  residentsCount: string
  availablePersons: string
  rentTime: string
  paymentTime: string
  priceIncludeWaterAndElectricity: boolean
  allowSmoking: boolean
  includeFurniture: boolean
  airConditioning: boolean
  includeWaterHeater: boolean
  parking: boolean
  internet: boolean
  nearToMetro: boolean
  nearToMarket: boolean
  elevator: boolean
  trialPeriod: boolean
  goodForForeigners: boolean
  categoryId: string
  isVerified?: boolean
  isAvailable?: boolean
  isFavorite?: boolean
  rating?: number
  totalRatings?: number
  user?: {
    name: string
    avatar?: string
  }
  createdAt: string
  updatedAt: string
}

// Person interface with similar structure to Property
interface Person {
  id: string
  title: string
  slug: string
  images: string[]
  city: string
  country: string
  neighborhood: string
  address: string
  description: string
  type: string
  roomType: string
  genderRequired: string
  totalRooms: string
  availableRooms: string
  price: string
  size: string
  floor: string
  bathrooms: string
  separatedBathroom: boolean
  residentsCount: string
  availablePersons: string
  rentTime: string
  paymentTime: string
  priceIncludeWaterAndElectricity: boolean
  allowSmoking: boolean
  includeFurniture: boolean
  airConditioning: boolean
  includeWaterHeater: boolean
  parking: boolean
  internet: boolean
  nearToMetro: boolean
  nearToMarket: boolean
  elevator: boolean
  trialPeriod: boolean
  goodForForeigners: boolean
  categoryId: string
  isVerified?: boolean
  isAvailable?: boolean
  isFavorite?: boolean
  rating?: number
  totalRatings?: number
  user?: {
    name: string
    avatar?: string
  }
  createdAt: string
  updatedAt: string
}

// Union type for items that can be either Property or Person
type PropertyOrPerson = Property | Person

interface HomepageProps {
  className?: string
}

export function MyAdsContent({ className }: HomepageProps) {
  const t = useTranslations('homepage')
  const queryClient = useQueryClient()

  // State management
  const [viewType, setViewType] = useState<ViewType>('properties')
  const [selectedCategory] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [sortBy] = useState<SortOption>('newest')
  const [viewMode] = useState<ViewMode>('grid')

  // Favorite hooks
  const { isPropertyFavorite, favorites: favoriteProperties } = useFavoriteProperties()
  const { isPersonFavorite, favorites: favoritePersons } = useFavoritePersons()

  console.log("favoriteProperties", favoriteProperties)
  console.log("favoritePersons", favoritePersons)


  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [viewType, selectedCategory, sortBy])

  // Map sort options to API parameters
  const getSortParams = (sortOption: SortOption) => {
    switch (sortOption) {
      case 'newest':
        return { sortBy: 'createdAt' as const, sortOrder: 'desc' as const }
      case 'oldest':
        return { sortBy: 'createdAt' as const, sortOrder: 'asc' as const }
      case 'price-high':
        return { sortBy: 'price' as const, sortOrder: 'desc' as const }
      case 'price-low':
        return { sortBy: 'price' as const, sortOrder: 'asc' as const }
      default:
        return { sortBy: 'createdAt' as const, sortOrder: 'desc' as const }
    }
  }

  const sortParams = getSortParams(sortBy)

  // Properties query
  const propertiesQuery = useQuery({
    queryKey: ['properties', selectedCategory, currentPage, sortBy],
    queryFn: () => getProperties({
      page: currentPage,
      limit: 12,
      categoryId: selectedCategory === 'all' ? '' : selectedCategory,
      ...sortParams
    }),
    enabled: viewType === 'properties',
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Persons query
  const personsQuery = useQuery({
    queryKey: ['persons', selectedCategory, currentPage, sortBy],
    queryFn: () => getPersons({
      page: currentPage,
      limit: 12,
      categoryId: selectedCategory === 'all' ? '' : selectedCategory,
      ...sortParams
    }),
    enabled: viewType === 'persons',
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Mutations for favorite toggle
  const togglePropertyFavoriteMutation = useMutation({
    mutationFn: (id: string) => togglePropertyFavorite(id),
    onSuccess: () => {
      // Invalidate and refetch properties data and favorites
      queryClient.invalidateQueries({ queryKey: ['properties'] })
      queryClient.invalidateQueries({ queryKey: ['favorites', 'properties'] })
    },
    onError: (error) => {
      console.error('Failed to toggle property favorite:', error)
    }
  })

  const togglePersonFavoriteMutation = useMutation({
    mutationFn: (id: string) => togglePersonFavorite(id),
    onSuccess: () => {
      // Invalidate and refetch persons data and favorites
      queryClient.invalidateQueries({ queryKey: ['persons'] })
      queryClient.invalidateQueries({ queryKey: ['favorites', 'persons'] })
    },
    onError: (error) => {
      console.error('Failed to toggle person favorite:', error)
    }
  })

  // Get current query based on view type
  const currentQuery = viewType === 'properties' ? propertiesQuery : personsQuery

  // Handle different API response structures
  // API service wraps response in { data: actualResponse, status: number }
  const apiResponse = currentQuery.data?.data

  const currentData = viewType === 'properties'
    ? apiResponse?.data || []
    : apiResponse?.data?.persons || []

  const currentPagination = viewType === 'properties'
    ? apiResponse?.pagination
    : apiResponse?.data?.pagination

  // Handle favorite toggle
  const handleFavoriteToggle = (id: string) => {
    if (viewType === 'properties') {
      togglePropertyFavoriteMutation.mutate(id)
    } else {
      togglePersonFavoriteMutation.mutate(id)
    }
  }

  // Handle load more
  const handleLoadMore = () => {
    if (currentPagination?.hasNext) {
      setCurrentPage(prev => prev + 1)
    }
  }

  // Loading state for initial load
  if (currentQuery.isLoading && currentPage === 1) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
          <p className="text-muted-foreground">
            {viewType === 'properties' ? t('loading.properties') : t('loading.persons')}
          </p>
        </div>
      </div>
    )
  }

  // Error state
  if (currentQuery.isError) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <AlertCircle className="h-8 w-8 mx-auto text-destructive" />
          <div>
            <h3 className="font-semibold mb-2">Something went wrong</h3>
            <p className="text-muted-foreground mb-4">
              {viewType === 'properties' ? t('errors.loadingProperties') : t('errors.loadingPersons')}
            </p>
            <Button onClick={() => currentQuery.refetch()}>
              Try Again
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("w-full space-y-6", className)}>
      {/* Filters Section */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 justify-between">
          <h1 className="text-3xl font-bold text-gray-900">My Ads</h1>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 justify-between">
            <TypeToggle value={viewType} onChange={setViewType} />
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="space-y-6">
        {/* Results Count */}
        {/* {currentPagination && (
                    <div className="text-sm text-muted-foreground">
                        {t('navigation.showingResults', {
                            current: currentData.length,
                            total: currentPagination.total
                        })}
                    </div>
                )} */}

        {/* Grid */}
        {currentData.length === 0 ? (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto space-y-4">
              <div className="flex justify-center">
                {viewType === 'properties' ? (
                  <Home className="h-16 w-16 text-muted-foreground animate-bounce" />
                ) : (
                  <UserX className="h-16 w-16 text-muted-foreground animate-bounce" />
                )}
              </div>
              <h3 className="text-lg font-semibold">
                {viewType === 'properties' ? t('empty.properties') : t('empty.persons')}
              </h3>
              <p className="text-muted-foreground">
                {t('empty.tryAdjusting')}
              </p>
            </div>
          </div>
        ) : (
          <div className={cn(
            "grid gap-6",
            viewMode === 'grid'
              ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
              : "grid-cols-1"
          )}>
            {Array.isArray(currentData) && currentData.map((item: PropertyOrPerson) => (
              viewType === 'properties' ? (
                <PropertyCard
                  key={item.id}
                  property={item}
                  onFavoriteToggle={handleFavoriteToggle}
                  viewType={viewType}
                  isFavoriteLoading={togglePropertyFavoriteMutation.isPending}
                  isFavorite={isPropertyFavorite(item.id)}
                />
              ) : (
                <PropertyCard
                  key={item.id}
                  property={item}
                  onFavoriteToggle={handleFavoriteToggle}
                  viewType={viewType}
                  isFavoriteLoading={togglePersonFavoriteMutation.isPending}
                  isFavorite={isPersonFavorite(item.id)}
                />
              )
            ))}
          </div>
        )}

        {/* Load More Button */}
        {currentPagination?.hasNext && (
          <div className="text-center">
            <Button
              onClick={handleLoadMore}
              disabled={currentQuery.isFetching}
              variant="outline"
              size="lg"
            >
              {currentQuery.isFetching ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Loading...
                </>
              ) : (
                t('navigation.loadMore')
              )}
            </Button>
          </div>
        )}

        {/* Loading indicator for additional pages */}
        {currentQuery.isFetching && currentPage > 1 && (
          <div className="text-center py-4">
            <Loader2 className="h-6 w-6 animate-spin mx-auto text-primary" />
          </div>
        )}
      </div>
    </div>
  )
} 